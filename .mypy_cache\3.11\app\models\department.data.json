{".class": "MypyFile", "_fullname": "app.models.department", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Base": {".class": "SymbolTableNode", "cross_ref": "app.core.database.Base", "kind": "Gdef"}, "BaseOrganizationEntity": {".class": "SymbolTableNode", "cross_ref": "app.models.base_organization.BaseOrganizationEntity", "kind": "Gdef"}, "Column": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Column", "kind": "Gdef"}, "Department": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["app.models.base_organization.BaseOrganizationEntity"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.models.department.Department", "name": "Department", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "app.models.department.Department", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app.models.department", "mro": ["app.models.department.Department", "app.models.base_organization.BaseOrganizationEntity", "builtins.object"], "names": {".class": "SymbolTable", "__tablename__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "app.models.department.Department.__tablename__", "name": "__tablename__", "setter_type": null, "type": "builtins.str"}}, "department_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.models.department.Department.department_name", "name": "department_name", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}}}, "description": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.models.department.Department.description", "name": "description", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}}}, "id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.models.department.Department.id", "name": "id", "setter_type": null, "type": {".class": "Instance", "args": ["uuid.UUID"], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}}}, "positions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.models.department.Department.positions", "name": "positions", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.relationships._RelationshipDeclared"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.models.department.Department.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.models.department.Department", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "String": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.String", "kind": "Gdef"}, "Text": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.Text", "kind": "Gdef"}, "UUID": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.UUID", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.models.department.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.models.department.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.models.department.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.models.department.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.models.department.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.models.department.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "relationship": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._orm_constructors.relationship", "kind": "Gdef"}, "uuid": {".class": "SymbolTableNode", "cross_ref": "uuid", "kind": "Gdef"}}, "path": ".\\app\\models\\department.py"}