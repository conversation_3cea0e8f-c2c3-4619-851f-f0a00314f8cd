{"data_mtime": 1752486191, "dep_lines": [4, 10, 11, 19, 20, 21, 28, 3, 3, 43, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 30, 30], "dependencies": ["wtforms.fields.choices", "wtforms.fields.core", "wtforms.fields.datetime", "wtforms.fields.form", "wtforms.fields.list", "wtforms.fields.numeric", "wtforms.fields.simple", "wtforms.validators", "wtforms.widgets", "wtforms.form", "typing", "builtins", "_frozen_importlib", "abc"], "hash": "3a7a48973898d565970d2353b2106bc42f754e1e", "id": "wtforms", "ignore_all": true, "interface_hash": "e05b8102f4577f3cabc132d8aa378c290aae82c4", "mtime": 1752485783, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Documents\\GitHub\\workshop\\.conda\\Lib\\site-packages\\wtforms-stubs\\__init__.pyi", "plugin_data": null, "size": 2229, "suppressed": [], "version_id": "1.16.1"}