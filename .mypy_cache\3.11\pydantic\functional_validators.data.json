{".class": "MypyFile", "_fullname": "pydantic.functional_validators", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AfterValidator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.functional_validators.AfterValidator", "name": "AfterValidator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic.functional_validators.AfterValidator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 71, "name": "func", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.NoInfoValidatorFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WithInfoValidatorFunction"}], "uses_pep604_syntax": true}}], "frozen": true}, "dataclass_tag": {}}, "module_name": "pydantic.functional_validators", "mro": ["pydantic.functional_validators.AfterValidator", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "pydantic.functional_validators.AfterValidator.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__get_pydantic_core_schema__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "source_type", "handler"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.functional_validators.AfterValidator.__get_pydantic_core_schema__", "name": "__get_pydantic_core_schema__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "source_type", "handler"], "arg_types": ["pydantic.functional_validators.AfterValidator", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "pydantic.annotated_handlers.GetCoreSchemaHandler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__get_pydantic_core_schema__ of AfterValidator", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "func"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.functional_validators.AfterValidator.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "func"], "arg_types": ["pydantic.functional_validators.AfterValidator", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.NoInfoValidatorFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WithInfoValidatorFunction"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AfterValidator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "pydantic.functional_validators.AfterValidator.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "func"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5], "arg_names": ["func"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "pydantic.functional_validators.AfterValidator.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["func"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.NoInfoValidatorFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WithInfoValidatorFunction"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of AfterValida<PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "pydantic.functional_validators.AfterValidator.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["func"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.NoInfoValidatorFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WithInfoValidatorFunction"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of AfterValida<PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "_from_decorator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "decorator"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "pydantic.functional_validators.AfterValidator._from_decorator", "name": "_from_decorator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "decorator"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators.AfterValidator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.functional_validators.AfterValidator", "values": [], "variance": 0}}, {".class": "Instance", "args": ["pydantic._internal._decorators.FieldValidatorDecoratorInfo"], "extra_attrs": null, "type_ref": "pydantic._internal._decorators.Decorator"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_from_decorator of AfterValidator", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators.AfterValidator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.functional_validators.AfterValidator", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators.AfterValidator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.functional_validators.AfterValidator", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic.functional_validators.AfterValidator._from_decorator", "name": "_from_decorator", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "decorator"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators.AfterValidator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.functional_validators.AfterValidator", "values": [], "variance": 0}}, {".class": "Instance", "args": ["pydantic._internal._decorators.FieldValidatorDecoratorInfo"], "extra_attrs": null, "type_ref": "pydantic._internal._decorators.Decorator"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_from_decorator of AfterValidator", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators.AfterValidator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.functional_validators.AfterValidator", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators.AfterValidator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.functional_validators.AfterValidator", "values": [], "variance": 0}]}}}}, "func": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "pydantic.functional_validators.AfterValidator.func", "name": "func", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.NoInfoValidatorFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WithInfoValidatorFunction"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators.AfterValidator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.functional_validators.AfterValidator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Annotated": {".class": "SymbolTableNode", "cross_ref": "typing.Annotated", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AnyType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators.AnyType", "name": "AnyType", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "BeforeValidator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.functional_validators.BeforeValidator", "name": "BeforeValidator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic.functional_validators.BeforeValidator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 122, "name": "func", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.NoInfoValidatorFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WithInfoValidatorFunction"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 123, "name": "json_schema_input_type", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "frozen": true}, "dataclass_tag": {}}, "module_name": "pydantic.functional_validators", "mro": ["pydantic.functional_validators.BeforeValidator", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "pydantic.functional_validators.BeforeValidator.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__get_pydantic_core_schema__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "source_type", "handler"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.functional_validators.BeforeValidator.__get_pydantic_core_schema__", "name": "__get_pydantic_core_schema__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "source_type", "handler"], "arg_types": ["pydantic.functional_validators.BeforeValidator", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "pydantic.annotated_handlers.GetCoreSchemaHandler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__get_pydantic_core_schema__ of BeforeValidator", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "func", "json_schema_input_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.functional_validators.BeforeValidator.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "func", "json_schema_input_type"], "arg_types": ["pydantic.functional_validators.BeforeValidator", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.NoInfoValidatorFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WithInfoValidatorFunction"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of BeforeValidator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "pydantic.functional_validators.BeforeValidator.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "func"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "json_schema_input_type"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5], "arg_names": ["func", "json_schema_input_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "pydantic.functional_validators.BeforeValidator.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["func", "json_schema_input_type"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.NoInfoValidatorFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WithInfoValidatorFunction"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of BeforeValida<PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "pydantic.functional_validators.BeforeValidator.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["func", "json_schema_input_type"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.NoInfoValidatorFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WithInfoValidatorFunction"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of BeforeValida<PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "_from_decorator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "decorator"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "pydantic.functional_validators.BeforeValidator._from_decorator", "name": "_from_decorator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "decorator"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators.BeforeValidator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.functional_validators.BeforeValidator", "values": [], "variance": 0}}, {".class": "Instance", "args": ["pydantic._internal._decorators.FieldValidatorDecoratorInfo"], "extra_attrs": null, "type_ref": "pydantic._internal._decorators.Decorator"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_from_decorator of BeforeValidator", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators.BeforeValidator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.functional_validators.BeforeValidator", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators.BeforeValidator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.functional_validators.BeforeValidator", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic.functional_validators.BeforeValidator._from_decorator", "name": "_from_decorator", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "decorator"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators.BeforeValidator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.functional_validators.BeforeValidator", "values": [], "variance": 0}}, {".class": "Instance", "args": ["pydantic._internal._decorators.FieldValidatorDecoratorInfo"], "extra_attrs": null, "type_ref": "pydantic._internal._decorators.Decorator"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_from_decorator of BeforeValidator", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators.BeforeValidator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.functional_validators.BeforeValidator", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators.BeforeValidator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.functional_validators.BeforeValidator", "values": [], "variance": 0}]}}}}, "func": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "pydantic.functional_validators.BeforeValidator.func", "name": "func", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.NoInfoValidatorFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WithInfoValidatorFunction"}], "uses_pep604_syntax": true}}}, "json_schema_input_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "has_explicit_value"], "fullname": "pydantic.functional_validators.BeforeValidator.json_schema_input_type", "name": "json_schema_input_type", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators.BeforeValidator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.functional_validators.BeforeValidator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "FieldValidatorModes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic.functional_validators.FieldValidatorModes", "line": 372, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "before"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "after"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wrap"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "plain"}], "uses_pep604_syntax": false}}}, "FreeModelBeforeValidator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__call__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.functional_validators.FreeModelBeforeValidator", "name": "FreeModelBeforeValidator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "pydantic.functional_validators.FreeModelBeforeValidator", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pydantic.functional_validators", "mro": ["pydantic.functional_validators.FreeModelBeforeValidator", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_trivial_self"], "fullname": "pydantic.functional_validators.FreeModelBeforeValidator.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["pydantic.functional_validators.FreeModelBeforeValidator", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "pydantic_core.core_schema.ValidationInfo"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of FreeModelBeforeValidator", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators.FreeModelBeforeValidator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.functional_validators.FreeModelBeforeValidator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FreeModelBeforeValidatorWithoutInfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__call__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.functional_validators.FreeModelBeforeValidatorWithoutInfo", "name": "FreeModelBeforeValidatorWithoutInfo", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "pydantic.functional_validators.FreeModelBeforeValidatorWithoutInfo", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pydantic.functional_validators", "mro": ["pydantic.functional_validators.FreeModelBeforeValidatorWithoutInfo", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_trivial_self"], "fullname": "pydantic.functional_validators.FreeModelBeforeValidatorWithoutInfo.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["pydantic.functional_validators.FreeModelBeforeValidatorWithoutInfo", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of FreeModelBeforeValidatorWithoutInfo", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators.FreeModelBeforeValidatorWithoutInfo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.functional_validators.FreeModelBeforeValidatorWithoutInfo", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FunctionType": {".class": "SymbolTableNode", "cross_ref": "types.FunctionType", "kind": "Gdef"}, "GetCoreSchemaHandler": {".class": "SymbolTableNode", "cross_ref": "pydantic.annotated_handlers.GetCoreSchemaHandler", "kind": "Gdef"}, "InstanceOf": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators.AnyType", "id": 1, "name": "AnyType", "namespace": "pydantic.functional_validators.InstanceOf", "upper_bound": "builtins.object", "values": [], "variance": 0}], "column": 4, "fullname": "pydantic.functional_validators.InstanceOf", "line": 732, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators.AnyType", "id": 1, "name": "AnyType", "namespace": "pydantic.functional_validators.InstanceOf", "upper_bound": "builtins.object", "values": [], "variance": 0}}}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "ModelAfterValidator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._ModelType", "id": 1, "name": "_ModelType", "namespace": "pydantic.functional_validators.ModelAfterValidator", "upper_bound": "builtins.object", "values": [], "variance": 0}], "column": 0, "fullname": "pydantic.functional_validators.ModelAfterValidator", "line": 632, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._ModelType", "id": 1, "name": "_ModelType", "namespace": "pydantic.functional_validators.ModelAfterValidator", "upper_bound": "builtins.object", "values": [], "variance": 0}, "pydantic_core.core_schema.ValidationInfo"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._ModelType", "id": 1, "name": "_ModelType", "namespace": "pydantic.functional_validators.ModelAfterValidator", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ModelAfterValidatorWithoutInfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._ModelType", "id": 1, "name": "_ModelType", "namespace": "pydantic.functional_validators.ModelAfterValidatorWithoutInfo", "upper_bound": "builtins.object", "values": [], "variance": 0}], "column": 0, "fullname": "pydantic.functional_validators.ModelAfterValidatorWithoutInfo", "line": 627, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._ModelType", "id": 1, "name": "_ModelType", "namespace": "pydantic.functional_validators.ModelAfterValidatorWithoutInfo", "upper_bound": "builtins.object", "values": [], "variance": 0}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._ModelType", "id": 1, "name": "_ModelType", "namespace": "pydantic.functional_validators.ModelAfterValidatorWithoutInfo", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ModelBeforeValidator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__call__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.functional_validators.ModelBeforeValidator", "name": "ModelBeforeValidator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "pydantic.functional_validators.ModelBeforeValidator", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pydantic.functional_validators", "mro": ["pydantic.functional_validators.ModelBeforeValidator", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_trivial_self"], "fullname": "pydantic.functional_validators.ModelBeforeValidator.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": ["pydantic.functional_validators.ModelBeforeValidator", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "pydantic_core.core_schema.ValidationInfo"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of ModelBeforeValidator", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators.ModelBeforeValidator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.functional_validators.ModelBeforeValidator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ModelBeforeValidatorWithoutInfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__call__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.functional_validators.ModelBeforeValidatorWithoutInfo", "name": "ModelBeforeValidatorWithoutInfo", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "pydantic.functional_validators.ModelBeforeValidatorWithoutInfo", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pydantic.functional_validators", "mro": ["pydantic.functional_validators.ModelBeforeValidatorWithoutInfo", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_trivial_self"], "fullname": "pydantic.functional_validators.ModelBeforeValidatorWithoutInfo.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["pydantic.functional_validators.ModelBeforeValidatorWithoutInfo", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of ModelBeforeValidatorWithoutInfo", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators.ModelBeforeValidatorWithoutInfo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.functional_validators.ModelBeforeValidatorWithoutInfo", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ModelWrapValidator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__call__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.functional_validators.ModelWrapValidator", "name": "ModelWrapValidator", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._ModelType", "id": 1, "name": "_ModelType", "namespace": "pydantic.functional_validators.ModelWrapValidator", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "pydantic.functional_validators.ModelWrapValidator", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pydantic.functional_validators", "mro": ["pydantic.functional_validators.ModelWrapValidator", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": [null, null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_trivial_self"], "fullname": "pydantic.functional_validators.ModelWrapValidator.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": [null, null, null, null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._ModelType", "id": 1, "name": "_ModelType", "namespace": "pydantic.functional_validators.ModelWrapValidator", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "pydantic.functional_validators.ModelWrapValidator"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._ModelType", "id": 1, "name": "_ModelType", "namespace": "pydantic.functional_validators.ModelWrapValidator", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._ModelType", "id": 1, "name": "_ModelType", "namespace": "pydantic.functional_validators.ModelWrapValidator", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "pydantic.functional_validators.ModelWrapValidatorHandler"}, "pydantic_core.core_schema.ValidationInfo"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of ModelWrapValidator", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._ModelType", "id": 1, "name": "_ModelType", "namespace": "pydantic.functional_validators.ModelWrapValidator", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators.ModelWrapValidator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._ModelType", "id": 1, "name": "_ModelType", "namespace": "pydantic.functional_validators.ModelWrapValidator", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "pydantic.functional_validators.ModelWrapValidator"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_ModelType"], "typeddict_type": null}}, "ModelWrapValidatorHandler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__call__", 2]], "alt_promote": null, "bases": ["pydantic_core.core_schema.ValidatorFunctionWrapHandler"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.functional_validators.ModelWrapValidatorHandler", "name": "ModelWrapValidatorHandler", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._ModelTypeCo", "id": 1, "name": "_ModelTypeCo", "namespace": "pydantic.functional_validators.ModelWrapValidatorHandler", "upper_bound": "builtins.object", "values": [], "variance": 1}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "pydantic.functional_validators.ModelWrapValidatorHandler", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pydantic.functional_validators", "mro": ["pydantic.functional_validators.ModelWrapValidatorHandler", "pydantic_core.core_schema.ValidatorFunctionWrapHandler", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 1], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_trivial_self"], "fullname": "pydantic.functional_validators.ModelWrapValidatorHandler.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": [null, null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._ModelTypeCo", "id": 1, "name": "_ModelTypeCo", "namespace": "pydantic.functional_validators.ModelWrapValidatorHandler", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "pydantic.functional_validators.ModelWrapValidatorHandler"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.str", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of ModelWrapValidatorHandler", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._ModelTypeCo", "id": 1, "name": "_ModelTypeCo", "namespace": "pydantic.functional_validators.ModelWrapValidatorHandler", "upper_bound": "builtins.object", "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators.ModelWrapValidatorHandler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._ModelTypeCo", "id": 1, "name": "_ModelTypeCo", "namespace": "pydantic.functional_validators.ModelWrapValidatorHandler", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "pydantic.functional_validators.ModelWrapValidatorHandler"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_ModelTypeCo"], "typeddict_type": null}}, "ModelWrapValidatorWithoutInfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__call__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.functional_validators.ModelWrapValidatorWithoutInfo", "name": "ModelWrapValidatorWithoutInfo", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._ModelType", "id": 1, "name": "_ModelType", "namespace": "pydantic.functional_validators.ModelWrapValidatorWithoutInfo", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "pydantic.functional_validators.ModelWrapValidatorWithoutInfo", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pydantic.functional_validators", "mro": ["pydantic.functional_validators.ModelWrapValidatorWithoutInfo", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_trivial_self"], "fullname": "pydantic.functional_validators.ModelWrapValidatorWithoutInfo.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._ModelType", "id": 1, "name": "_ModelType", "namespace": "pydantic.functional_validators.ModelWrapValidatorWithoutInfo", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "pydantic.functional_validators.ModelWrapValidatorWithoutInfo"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._ModelType", "id": 1, "name": "_ModelType", "namespace": "pydantic.functional_validators.ModelWrapValidatorWithoutInfo", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._ModelType", "id": 1, "name": "_ModelType", "namespace": "pydantic.functional_validators.ModelWrapValidatorWithoutInfo", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "pydantic.functional_validators.ModelWrapValidatorHandler"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of ModelWrapValidatorWithoutInfo", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._ModelType", "id": 1, "name": "_ModelType", "namespace": "pydantic.functional_validators.ModelWrapValidatorWithoutInfo", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators.ModelWrapValidatorWithoutInfo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._ModelType", "id": 1, "name": "_ModelType", "namespace": "pydantic.functional_validators.ModelWrapValidatorWithoutInfo", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "pydantic.functional_validators.ModelWrapValidatorWithoutInfo"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_ModelType"], "typeddict_type": null}}, "PlainValidator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.functional_validators.PlainValidator", "name": "PlainValidator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic.functional_validators.PlainValidator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 200, "name": "func", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.NoInfoValidatorFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WithInfoValidatorFunction"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 201, "name": "json_schema_input_type", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "frozen": true}, "dataclass_tag": {}}, "module_name": "pydantic.functional_validators", "mro": ["pydantic.functional_validators.PlainValidator", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "pydantic.functional_validators.PlainValidator.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__get_pydantic_core_schema__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "source_type", "handler"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.functional_validators.PlainValidator.__get_pydantic_core_schema__", "name": "__get_pydantic_core_schema__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "source_type", "handler"], "arg_types": ["pydantic.functional_validators.PlainValidator", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "pydantic.annotated_handlers.GetCoreSchemaHandler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__get_pydantic_core_schema__ of PlainValidator", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "func", "json_schema_input_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.functional_validators.PlainValidator.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "func", "json_schema_input_type"], "arg_types": ["pydantic.functional_validators.PlainValidator", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.NoInfoValidatorFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WithInfoValidatorFunction"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of PlainValidator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "pydantic.functional_validators.PlainValidator.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "func"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "json_schema_input_type"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5], "arg_names": ["func", "json_schema_input_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "pydantic.functional_validators.PlainValidator.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["func", "json_schema_input_type"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.NoInfoValidatorFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WithInfoValidatorFunction"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of PlainValidator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "pydantic.functional_validators.PlainValidator.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["func", "json_schema_input_type"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.NoInfoValidatorFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WithInfoValidatorFunction"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of PlainValidator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "_from_decorator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "decorator"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "pydantic.functional_validators.PlainValidator._from_decorator", "name": "_from_decorator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "decorator"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators.PlainValidator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.functional_validators.PlainValidator", "values": [], "variance": 0}}, {".class": "Instance", "args": ["pydantic._internal._decorators.FieldValidatorDecoratorInfo"], "extra_attrs": null, "type_ref": "pydantic._internal._decorators.Decorator"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_from_decorator of PlainValidator", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators.PlainValidator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.functional_validators.PlainValidator", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators.PlainValidator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.functional_validators.PlainValidator", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic.functional_validators.PlainValidator._from_decorator", "name": "_from_decorator", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "decorator"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators.PlainValidator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.functional_validators.PlainValidator", "values": [], "variance": 0}}, {".class": "Instance", "args": ["pydantic._internal._decorators.FieldValidatorDecoratorInfo"], "extra_attrs": null, "type_ref": "pydantic._internal._decorators.Decorator"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_from_decorator of PlainValidator", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators.PlainValidator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.functional_validators.PlainValidator", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators.PlainValidator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.functional_validators.PlainValidator", "values": [], "variance": 0}]}}}}, "func": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "pydantic.functional_validators.PlainValidator.func", "name": "func", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.NoInfoValidatorFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WithInfoValidatorFunction"}], "uses_pep604_syntax": true}}}, "json_schema_input_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "has_explicit_value"], "fullname": "pydantic.functional_validators.PlainValidator.json_schema_input_type", "name": "json_schema_input_type", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators.PlainValidator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.functional_validators.PlainValidator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing.Protocol", "kind": "Gdef"}, "PydanticUndefined": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.PydanticUndefined", "kind": "Gdef"}, "PydanticUserError": {".class": "SymbolTableNode", "cross_ref": "pydantic.errors.PydanticUserError", "kind": "Gdef"}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing.Self", "kind": "Gdef"}, "SkipValidation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators.AnyType", "id": 1, "name": "AnyType", "namespace": "pydantic.functional_validators.SkipValidation", "upper_bound": "builtins.object", "values": [], "variance": 0}], "column": 4, "fullname": "pydantic.functional_validators.SkipValidation", "line": 798, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators.AnyType", "id": 1, "name": "AnyType", "namespace": "pydantic.functional_validators.SkipValidation", "upper_bound": "builtins.object", "values": [], "variance": 0}}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "WrapValidator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.functional_validators.WrapValidator", "name": "WrapValidator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic.functional_validators.WrapValidator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 293, "name": "func", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.NoInfoWrapValidatorFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WithInfoWrapValidatorFunction"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 294, "name": "json_schema_input_type", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "frozen": true}, "dataclass_tag": {}}, "module_name": "pydantic.functional_validators", "mro": ["pydantic.functional_validators.WrapValidator", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "pydantic.functional_validators.WrapValidator.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__get_pydantic_core_schema__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "source_type", "handler"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.functional_validators.WrapValidator.__get_pydantic_core_schema__", "name": "__get_pydantic_core_schema__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "source_type", "handler"], "arg_types": ["pydantic.functional_validators.WrapValidator", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "pydantic.annotated_handlers.GetCoreSchemaHandler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__get_pydantic_core_schema__ of WrapValidator", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "func", "json_schema_input_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.functional_validators.WrapValidator.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "func", "json_schema_input_type"], "arg_types": ["pydantic.functional_validators.WrapValidator", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.NoInfoWrapValidatorFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WithInfoWrapValidatorFunction"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of WrapValidator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "pydantic.functional_validators.WrapValidator.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "func"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "json_schema_input_type"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5], "arg_names": ["func", "json_schema_input_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "pydantic.functional_validators.WrapValidator.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["func", "json_schema_input_type"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.NoInfoWrapValidatorFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WithInfoWrapValidatorFunction"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of WrapValidator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "pydantic.functional_validators.WrapValidator.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["func", "json_schema_input_type"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.NoInfoWrapValidatorFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WithInfoWrapValidatorFunction"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of WrapValidator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "_from_decorator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "decorator"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "pydantic.functional_validators.WrapValidator._from_decorator", "name": "_from_decorator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "decorator"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators.WrapValidator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.functional_validators.WrapValidator", "values": [], "variance": 0}}, {".class": "Instance", "args": ["pydantic._internal._decorators.FieldValidatorDecoratorInfo"], "extra_attrs": null, "type_ref": "pydantic._internal._decorators.Decorator"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_from_decorator of WrapValidator", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators.WrapValidator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.functional_validators.WrapValidator", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators.WrapValidator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.functional_validators.WrapValidator", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic.functional_validators.WrapValidator._from_decorator", "name": "_from_decorator", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "decorator"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators.WrapValidator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.functional_validators.WrapValidator", "values": [], "variance": 0}}, {".class": "Instance", "args": ["pydantic._internal._decorators.FieldValidatorDecoratorInfo"], "extra_attrs": null, "type_ref": "pydantic._internal._decorators.Decorator"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_from_decorator of WrapValidator", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators.WrapValidator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.functional_validators.WrapValidator", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators.WrapValidator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.functional_validators.WrapValidator", "values": [], "variance": 0}]}}}}, "func": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "pydantic.functional_validators.WrapValidator.func", "name": "func", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.NoInfoWrapValidatorFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WithInfoWrapValidatorFunction"}], "uses_pep604_syntax": true}}}, "json_schema_input_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "has_explicit_value"], "fullname": "pydantic.functional_validators.WrapValidator.json_schema_input_type", "name": "json_schema_input_type", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators.WrapValidator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.functional_validators.WrapValidator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_AnyModelAfterValidator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._ModelType", "id": 1, "name": "_ModelType", "namespace": "pydantic.functional_validators._AnyModelAfterValidator", "upper_bound": "builtins.object", "values": [], "variance": 0}], "column": 0, "fullname": "pydantic.functional_validators._AnyModelAfterValidator", "line": 639, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._ModelType", "id": 1, "name": "_ModelType", "namespace": "pydantic.functional_validators._AnyModelAfterValidator", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic.functional_validators.ModelAfterValidator"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._ModelType", "id": 1, "name": "_ModelType", "namespace": "pydantic.functional_validators._AnyModelAfterValidator", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic.functional_validators.ModelAfterValidatorWithoutInfo"}], "uses_pep604_syntax": false}}}, "_AnyModelBeforeValidator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic.functional_validators._AnyModelBeforeValidator", "line": 636, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["pydantic.functional_validators.FreeModelBeforeValidator", "pydantic.functional_validators.ModelBeforeValidator", "pydantic.functional_validators.FreeModelBeforeValidatorWithoutInfo", "pydantic.functional_validators.ModelBeforeValidatorWithoutInfo"], "uses_pep604_syntax": false}}}, "_AnyModelWrapValidator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._ModelType", "id": 1, "name": "_ModelType", "namespace": "pydantic.functional_validators._AnyModelWrapValidator", "upper_bound": "builtins.object", "values": [], "variance": 0}], "column": 0, "fullname": "pydantic.functional_validators._AnyModelWrapValidator", "line": 635, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._ModelType", "id": 1, "name": "_ModelType", "namespace": "pydantic.functional_validators._AnyModelWrapValidator", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "pydantic.functional_validators.ModelWrapValidator"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._ModelType", "id": 1, "name": "_ModelType", "namespace": "pydantic.functional_validators._AnyModelWrapValidator", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "pydantic.functional_validators.ModelWrapValidatorWithoutInfo"}], "uses_pep604_syntax": false}}}, "_ModelType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._ModelType", "name": "_ModelType", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "_ModelTypeCo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._ModelTypeCo", "name": "_ModelTypeCo", "upper_bound": "builtins.object", "values": [], "variance": 1}}, "_OnlyValueValidatorClsMethod": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__call__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.functional_validators._OnlyValueValidatorClsMethod", "name": "_OnlyValueValidatorClsMethod", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "pydantic.functional_validators._OnlyValueValidatorClsMethod", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pydantic.functional_validators", "mro": ["pydantic.functional_validators._OnlyValueValidatorClsMethod", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_trivial_self", "is_mypy_only"], "fullname": "pydantic.functional_validators._OnlyValueValidatorClsMethod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["pydantic.functional_validators._OnlyValueValidatorClsMethod", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _OnlyValueValidatorClsMethod", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._OnlyValueValidatorClsMethod.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.functional_validators._OnlyValueValidatorClsMethod", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_OnlyValueWrapValidatorClsMethod": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__call__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.functional_validators._OnlyValueWrapValidatorClsMethod", "name": "_OnlyValueWrapValidatorClsMethod", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "pydantic.functional_validators._OnlyValueWrapValidatorClsMethod", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pydantic.functional_validators", "mro": ["pydantic.functional_validators._OnlyValueWrapValidatorClsMethod", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_trivial_self", "is_mypy_only"], "fullname": "pydantic.functional_validators._OnlyValueWrapValidatorClsMethod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": ["pydantic.functional_validators._OnlyValueWrapValidatorClsMethod", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "pydantic_core.core_schema.ValidatorFunctionWrapHandler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _OnlyValueWrapValidatorClsMethod", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._OnlyValueWrapValidatorClsMethod.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.functional_validators._OnlyValueWrapValidatorClsMethod", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_PartialClsOrStaticMethod": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "pydantic.functional_validators._PartialClsOrStaticMethod", "line": 364, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.classmethod"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.staticmethod"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "functools.partialmethod"}], "uses_pep604_syntax": false}}}, "_V2BeforeAfterOrPlainValidatorType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._V2BeforeAfterOrPlainValidatorType", "name": "_V2BeforeAfterOrPlainValidatorType", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._V2Validator"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._PartialClsOrStaticMethod"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, "_V2Validator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "pydantic.functional_validators._V2Validator", "line": 350, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["pydantic.functional_validators._V2ValidatorClsMethod", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WithInfoValidatorFunction"}, "pydantic.functional_validators._OnlyValueValidatorClsMethod", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.NoInfoValidatorFunction"}], "uses_pep604_syntax": false}}}, "_V2ValidatorClsMethod": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__call__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.functional_validators._V2ValidatorClsMethod", "name": "_V2ValidatorClsMethod", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "pydantic.functional_validators._V2ValidatorClsMethod", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pydantic.functional_validators", "mro": ["pydantic.functional_validators._V2ValidatorClsMethod", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_trivial_self", "is_mypy_only"], "fullname": "pydantic.functional_validators._V2ValidatorClsMethod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": ["pydantic.functional_validators._V2ValidatorClsMethod", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "pydantic_core.core_schema.ValidationInfo"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _V2ValidatorClsMethod", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._V2ValidatorClsMethod.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.functional_validators._V2ValidatorClsMethod", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_V2WrapValidator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "pydantic.functional_validators._V2WrapValidator", "line": 357, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["pydantic.functional_validators._V2WrapValidatorClsMethod", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WithInfoWrapValidatorFunction"}, "pydantic.functional_validators._OnlyValueWrapValidatorClsMethod", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.NoInfoWrapValidatorFunction"}], "uses_pep604_syntax": false}}}, "_V2WrapValidatorClsMethod": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__call__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic.functional_validators._V2WrapValidatorClsMethod", "name": "_V2WrapValidatorClsMethod", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "pydantic.functional_validators._V2WrapValidatorClsMethod", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pydantic.functional_validators", "mro": ["pydantic.functional_validators._V2WrapValidatorClsMethod", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": [null, null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_trivial_self", "is_mypy_only"], "fullname": "pydantic.functional_validators._V2WrapValidatorClsMethod.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": [null, null, null, null, null], "arg_types": ["pydantic.functional_validators._V2WrapValidatorClsMethod", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "pydantic_core.core_schema.ValidatorFunctionWrapHandler", "pydantic_core.core_schema.ValidationInfo"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _V2WrapValidatorClsMethod", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._V2WrapValidatorClsMethod.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.functional_validators._V2WrapValidatorClsMethod", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_V2WrapValidatorType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._V2WrapValidatorType", "name": "_V2WrapValidatorType", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._V2WrapValidator"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._PartialClsOrStaticMethod"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.functional_validators.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.functional_validators.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.functional_validators.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.functional_validators.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.functional_validators.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.functional_validators.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "_core_schema": {".class": "SymbolTableNode", "cross_ref": "pydantic_core.core_schema", "kind": "Gdef"}, "_decorators": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._decorators", "kind": "Gdef"}, "_generics": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._generics", "kind": "Gdef"}, "_inspect_validator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic.functional_validators._inspect_validator", "name": "_inspect_validator", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["validator", "mode"], "arg_types": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators.FieldValidatorModes"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_internal_dataclass": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._internal_dataclass", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "core_schema": {".class": "SymbolTableNode", "cross_ref": "pydantic_core.core_schema", "kind": "Gdef"}, "dataclasses": {".class": "SymbolTableNode", "cross_ref": "dataclasses", "kind": "Gdef"}, "field_validator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "pydantic.functional_validators.field_validator", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5, 5], "arg_names": [null, "fields", "mode", "check_fields", "json_schema_input_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "pydantic.functional_validators.field_validator", "name": "field_validator", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5, 5], "arg_names": [null, "fields", "mode", "check_fields", "json_schema_input_type"], "arg_types": ["builtins.str", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators.FieldValidatorModes"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "field_validator", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 3, 5, 5], "arg_names": [null, "fields", "mode", "check_fields", "json_schema_input_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "pydantic.functional_validators.field_validator", "name": "field_validator", "type": {".class": "CallableType", "arg_kinds": [0, 2, 3, 5, 5], "arg_names": [null, "fields", "mode", "check_fields", "json_schema_input_type"], "arg_types": ["builtins.str", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "wrap"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "field_validator", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._V2WrapValidatorType", "id": -1, "name": "_V2WrapValidatorType", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._V2WrapValidator"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._PartialClsOrStaticMethod"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._V2WrapValidatorType", "id": -1, "name": "_V2WrapValidatorType", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._V2WrapValidator"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._PartialClsOrStaticMethod"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._V2WrapValidatorType", "id": -1, "name": "_V2WrapValidatorType", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._V2WrapValidator"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._PartialClsOrStaticMethod"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}]}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "pydantic.functional_validators.field_validator", "name": "field_validator", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 2, 3, 5, 5], "arg_names": [null, "fields", "mode", "check_fields", "json_schema_input_type"], "arg_types": ["builtins.str", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "wrap"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "field_validator", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._V2WrapValidatorType", "id": -1, "name": "_V2WrapValidatorType", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._V2WrapValidator"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._PartialClsOrStaticMethod"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._V2WrapValidatorType", "id": -1, "name": "_V2WrapValidatorType", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._V2WrapValidator"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._PartialClsOrStaticMethod"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._V2WrapValidatorType", "id": -1, "name": "_V2WrapValidatorType", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._V2WrapValidator"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._PartialClsOrStaticMethod"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}]}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 3, 5, 5], "arg_names": [null, "fields", "mode", "check_fields", "json_schema_input_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "pydantic.functional_validators.field_validator", "name": "field_validator", "type": {".class": "CallableType", "arg_kinds": [0, 2, 3, 5, 5], "arg_names": [null, "fields", "mode", "check_fields", "json_schema_input_type"], "arg_types": ["builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "before"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "plain"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "field_validator", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._V2BeforeAfterOrPlainValidatorType", "id": -1, "name": "_V2BeforeAfterOrPlainValidatorType", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._V2Validator"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._PartialClsOrStaticMethod"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._V2BeforeAfterOrPlainValidatorType", "id": -1, "name": "_V2BeforeAfterOrPlainValidatorType", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._V2Validator"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._PartialClsOrStaticMethod"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._V2BeforeAfterOrPlainValidatorType", "id": -1, "name": "_V2BeforeAfterOrPlainValidatorType", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._V2Validator"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._PartialClsOrStaticMethod"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}]}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "pydantic.functional_validators.field_validator", "name": "field_validator", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 2, 3, 5, 5], "arg_names": [null, "fields", "mode", "check_fields", "json_schema_input_type"], "arg_types": ["builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "before"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "plain"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "field_validator", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._V2BeforeAfterOrPlainValidatorType", "id": -1, "name": "_V2BeforeAfterOrPlainValidatorType", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._V2Validator"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._PartialClsOrStaticMethod"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._V2BeforeAfterOrPlainValidatorType", "id": -1, "name": "_V2BeforeAfterOrPlainValidatorType", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._V2Validator"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._PartialClsOrStaticMethod"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._V2BeforeAfterOrPlainValidatorType", "id": -1, "name": "_V2BeforeAfterOrPlainValidatorType", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._V2Validator"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._PartialClsOrStaticMethod"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}]}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5], "arg_names": [null, "fields", "mode", "check_fields"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "pydantic.functional_validators.field_validator", "name": "field_validator", "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5], "arg_names": [null, "fields", "mode", "check_fields"], "arg_types": ["builtins.str", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "after"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "field_validator", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._V2BeforeAfterOrPlainValidatorType", "id": -1, "name": "_V2BeforeAfterOrPlainValidatorType", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._V2Validator"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._PartialClsOrStaticMethod"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._V2BeforeAfterOrPlainValidatorType", "id": -1, "name": "_V2BeforeAfterOrPlainValidatorType", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._V2Validator"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._PartialClsOrStaticMethod"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._V2BeforeAfterOrPlainValidatorType", "id": -1, "name": "_V2BeforeAfterOrPlainValidatorType", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._V2Validator"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._PartialClsOrStaticMethod"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}]}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "pydantic.functional_validators.field_validator", "name": "field_validator", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 2, 5, 5], "arg_names": [null, "fields", "mode", "check_fields"], "arg_types": ["builtins.str", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "after"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "field_validator", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._V2BeforeAfterOrPlainValidatorType", "id": -1, "name": "_V2BeforeAfterOrPlainValidatorType", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._V2Validator"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._PartialClsOrStaticMethod"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._V2BeforeAfterOrPlainValidatorType", "id": -1, "name": "_V2BeforeAfterOrPlainValidatorType", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._V2Validator"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._PartialClsOrStaticMethod"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._V2BeforeAfterOrPlainValidatorType", "id": -1, "name": "_V2BeforeAfterOrPlainValidatorType", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._V2Validator"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._PartialClsOrStaticMethod"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}]}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 2, 3, 5, 5], "arg_names": [null, "fields", "mode", "check_fields", "json_schema_input_type"], "arg_types": ["builtins.str", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "wrap"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "field_validator", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._V2WrapValidatorType", "id": -1, "name": "_V2WrapValidatorType", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._V2WrapValidator"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._PartialClsOrStaticMethod"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._V2WrapValidatorType", "id": -1, "name": "_V2WrapValidatorType", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._V2WrapValidator"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._PartialClsOrStaticMethod"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._V2WrapValidatorType", "id": -1, "name": "_V2WrapValidatorType", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._V2WrapValidator"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._PartialClsOrStaticMethod"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}]}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 2, 3, 5, 5], "arg_names": [null, "fields", "mode", "check_fields", "json_schema_input_type"], "arg_types": ["builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "before"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "plain"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "field_validator", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._V2BeforeAfterOrPlainValidatorType", "id": -1, "name": "_V2BeforeAfterOrPlainValidatorType", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._V2Validator"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._PartialClsOrStaticMethod"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._V2BeforeAfterOrPlainValidatorType", "id": -1, "name": "_V2BeforeAfterOrPlainValidatorType", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._V2Validator"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._PartialClsOrStaticMethod"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._V2BeforeAfterOrPlainValidatorType", "id": -1, "name": "_V2BeforeAfterOrPlainValidatorType", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._V2Validator"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._PartialClsOrStaticMethod"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}]}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 2, 5, 5], "arg_names": [null, "fields", "mode", "check_fields"], "arg_types": ["builtins.str", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "after"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "field_validator", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._V2BeforeAfterOrPlainValidatorType", "id": -1, "name": "_V2BeforeAfterOrPlainValidatorType", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._V2Validator"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._PartialClsOrStaticMethod"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._V2BeforeAfterOrPlainValidatorType", "id": -1, "name": "_V2BeforeAfterOrPlainValidatorType", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._V2Validator"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._PartialClsOrStaticMethod"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._V2BeforeAfterOrPlainValidatorType", "id": -1, "name": "_V2BeforeAfterOrPlainValidatorType", "namespace": "", "upper_bound": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._V2Validator"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._PartialClsOrStaticMethod"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}]}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "model_validator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "pydantic.functional_validators.model_validator", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [3], "arg_names": ["mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "pydantic.functional_validators.model_validator", "name": "model_validator", "type": {".class": "CallableType", "arg_kinds": [3], "arg_names": ["mode"], "arg_types": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "wrap"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "before"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "after"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_validator", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [3], "arg_names": ["mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "pydantic.functional_validators.model_validator", "name": "model_validator", "type": {".class": "CallableType", "arg_kinds": [3], "arg_names": ["mode"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "wrap"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_validator", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._ModelType", "id": -1, "name": "_ModelType", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic.functional_validators._AnyModelWrapValidator"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["pydantic._internal._decorators.ModelValidatorDecoratorInfo"], "extra_attrs": null, "type_ref": "pydantic._internal._decorators.PydanticDescriptorProxy"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._ModelType", "id": -1, "name": "_ModelType", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "pydantic.functional_validators.model_validator", "name": "model_validator", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [3], "arg_names": ["mode"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "wrap"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_validator", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._ModelType", "id": -1, "name": "_ModelType", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic.functional_validators._AnyModelWrapValidator"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["pydantic._internal._decorators.ModelValidatorDecoratorInfo"], "extra_attrs": null, "type_ref": "pydantic._internal._decorators.PydanticDescriptorProxy"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._ModelType", "id": -1, "name": "_ModelType", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [3], "arg_names": ["mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "pydantic.functional_validators.model_validator", "name": "model_validator", "type": {".class": "CallableType", "arg_kinds": [3], "arg_names": ["mode"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "before"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_validator", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._AnyModelBeforeValidator"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["pydantic._internal._decorators.ModelValidatorDecoratorInfo"], "extra_attrs": null, "type_ref": "pydantic._internal._decorators.PydanticDescriptorProxy"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "pydantic.functional_validators.model_validator", "name": "model_validator", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [3], "arg_names": ["mode"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "before"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_validator", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._AnyModelBeforeValidator"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["pydantic._internal._decorators.ModelValidatorDecoratorInfo"], "extra_attrs": null, "type_ref": "pydantic._internal._decorators.PydanticDescriptorProxy"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [3], "arg_names": ["mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "pydantic.functional_validators.model_validator", "name": "model_validator", "type": {".class": "CallableType", "arg_kinds": [3], "arg_names": ["mode"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "after"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_validator", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._ModelType", "id": -1, "name": "_ModelType", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic.functional_validators._AnyModelAfterValidator"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["pydantic._internal._decorators.ModelValidatorDecoratorInfo"], "extra_attrs": null, "type_ref": "pydantic._internal._decorators.PydanticDescriptorProxy"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._ModelType", "id": -1, "name": "_ModelType", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "pydantic.functional_validators.model_validator", "name": "model_validator", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [3], "arg_names": ["mode"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "after"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_validator", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._ModelType", "id": -1, "name": "_ModelType", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic.functional_validators._AnyModelAfterValidator"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["pydantic._internal._decorators.ModelValidatorDecoratorInfo"], "extra_attrs": null, "type_ref": "pydantic._internal._decorators.PydanticDescriptorProxy"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._ModelType", "id": -1, "name": "_ModelType", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [3], "arg_names": ["mode"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "wrap"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_validator", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._ModelType", "id": -1, "name": "_ModelType", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic.functional_validators._AnyModelWrapValidator"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["pydantic._internal._decorators.ModelValidatorDecoratorInfo"], "extra_attrs": null, "type_ref": "pydantic._internal._decorators.PydanticDescriptorProxy"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._ModelType", "id": -1, "name": "_ModelType", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [3], "arg_names": ["mode"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "before"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_validator", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.functional_validators._AnyModelBeforeValidator"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["pydantic._internal._decorators.ModelValidatorDecoratorInfo"], "extra_attrs": null, "type_ref": "pydantic._internal._decorators.PydanticDescriptorProxy"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [3], "arg_names": ["mode"], "arg_types": [{".class": "LiteralType", "fallback": "builtins.str", "value": "after"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_validator", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._ModelType", "id": -1, "name": "_ModelType", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "pydantic.functional_validators._AnyModelAfterValidator"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["pydantic._internal._decorators.ModelValidatorDecoratorInfo"], "extra_attrs": null, "type_ref": "pydantic._internal._decorators.PydanticDescriptorProxy"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.functional_validators._ModelType", "id": -1, "name": "_ModelType", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef"}, "partialmethod": {".class": "SymbolTableNode", "cross_ref": "functools.partialmethod", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\Documents\\GitHub\\workshop\\.conda\\Lib\\site-packages\\pydantic\\functional_validators.py"}