{".class": "MypyFile", "_fullname": "urllib3.contrib.socks", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ConnectTimeoutError": {".class": "SymbolTableNode", "cross_ref": "urllib3.exceptions.ConnectTimeoutError", "kind": "Gdef"}, "DependencyWarning": {".class": "SymbolTableNode", "cross_ref": "urllib3.exceptions.DependencyWarning", "kind": "Gdef"}, "HTTPConnection": {".class": "SymbolTableNode", "cross_ref": "urllib3.connection.HTTPConnection", "kind": "Gdef"}, "HTTPConnectionPool": {".class": "SymbolTableNode", "cross_ref": "urllib3.connectionpool.HTTPConnectionPool", "kind": "Gdef"}, "HTTPSConnection": {".class": "SymbolTableNode", "cross_ref": "urllib3.connection.HTTPSConnection", "kind": "Gdef"}, "HTTPSConnectionPool": {".class": "SymbolTableNode", "cross_ref": "urllib3.connectionpool.HTTPSConnectionPool", "kind": "Gdef"}, "NewConnectionError": {".class": "SymbolTableNode", "cross_ref": "urllib3.exceptions.NewConnectionError", "kind": "Gdef"}, "PoolManager": {".class": "SymbolTableNode", "cross_ref": "urllib3.poolmanager.PoolManager", "kind": "Gdef"}, "SOCKSConnection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["urllib3.connection.HTTPConnection"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.contrib.socks.SOCKSConnection", "name": "SOCKSConnection", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "urllib3.contrib.socks.SOCKSConnection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.contrib.socks", "mro": ["urllib3.contrib.socks.SOCKSConnection", "urllib3.connection.HTTPConnection", "http.client.HTTPConnection", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "_socks_options", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "urllib3.contrib.socks.SOCKSConnection.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "_socks_options", "args", "kwargs"], "arg_types": ["urllib3.contrib.socks.SOCKSConnection", {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.contrib.socks._TYPE_SOCKS_OPTIONS"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of SOCKSConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_new_conn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "urllib3.contrib.socks.SOCKSConnection._new_conn", "name": "_new_conn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["urllib3.contrib.socks.SOCKSConnection"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_new_conn of SOCKSConnection", "ret_type": {".class": "AnyType", "missing_import_name": "urllib3.contrib.socks.socks", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_socks_options": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.contrib.socks.SOCKSConnection._socks_options", "name": "_socks_options", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.contrib.socks._TYPE_SOCKS_OPTIONS"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.contrib.socks.SOCKSConnection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.contrib.socks.SOCKSConnection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SOCKSHTTPConnectionPool": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["urllib3.connectionpool.HTTPConnectionPool"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.contrib.socks.SOCKSHTTPConnectionPool", "name": "SOCKSHTTPConnectionPool", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "urllib3.contrib.socks.SOCKSHTTPConnectionPool", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.contrib.socks", "mro": ["urllib3.contrib.socks.SOCKSHTTPConnectionPool", "urllib3.connectionpool.HTTPConnectionPool", "urllib3.connectionpool.ConnectionPool", "urllib3._request_methods.RequestMethods", "builtins.object"], "names": {".class": "SymbolTable", "ConnectionCls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "urllib3.contrib.socks.SOCKSHTTPConnectionPool.ConnectionCls", "name": "ConnectionCls", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["_socks_options", "args", "kwargs"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.contrib.socks._TYPE_SOCKS_OPTIONS"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": true, "is_ellipsis_args": false, "name": null, "ret_type": "urllib3.contrib.socks.SOCKSConnection", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.contrib.socks.SOCKSHTTPConnectionPool.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.contrib.socks.SOCKSHTTPConnectionPool", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SOCKSHTTPSConnection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["urllib3.contrib.socks.SOCKSConnection", "urllib3.connection.HTTPSConnection"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.contrib.socks.SOCKSHTTPSConnection", "name": "SOCKSHTTPSConnection", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "urllib3.contrib.socks.SOCKSHTTPSConnection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.contrib.socks", "mro": ["urllib3.contrib.socks.SOCKSHTTPSConnection", "urllib3.contrib.socks.SOCKSConnection", "urllib3.connection.HTTPSConnection", "urllib3.connection.HTTPConnection", "http.client.HTTPConnection", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.contrib.socks.SOCKSHTTPSConnection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.contrib.socks.SOCKSHTTPSConnection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SOCKSHTTPSConnectionPool": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["urllib3.connectionpool.HTTPSConnectionPool"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.contrib.socks.SOCKSHTTPSConnectionPool", "name": "SOCKSHTTPSConnectionPool", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "urllib3.contrib.socks.SOCKSHTTPSConnectionPool", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.contrib.socks", "mro": ["urllib3.contrib.socks.SOCKSHTTPSConnectionPool", "urllib3.connectionpool.HTTPSConnectionPool", "urllib3.connectionpool.HTTPConnectionPool", "urllib3.connectionpool.ConnectionPool", "urllib3._request_methods.RequestMethods", "builtins.object"], "names": {".class": "SymbolTable", "ConnectionCls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "urllib3.contrib.socks.SOCKSHTTPSConnectionPool.ConnectionCls", "name": "ConnectionCls", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["_socks_options", "args", "kwargs"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.contrib.socks._TYPE_SOCKS_OPTIONS"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": true, "is_ellipsis_args": false, "name": null, "ret_type": "urllib3.contrib.socks.SOCKSHTTPSConnection", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.contrib.socks.SOCKSHTTPSConnectionPool.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.contrib.socks.SOCKSHTTPSConnectionPool", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SOCKSProxyManager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["urllib3.poolmanager.PoolManager"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.contrib.socks.SOCKSProxyManager", "name": "SOCKSProxyManager", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "urllib3.contrib.socks.SOCKSProxyManager", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.contrib.socks", "mro": ["urllib3.contrib.socks.SOCKSProxyManager", "urllib3.poolmanager.PoolManager", "urllib3._request_methods.RequestMethods", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 4], "arg_names": ["self", "proxy_url", "username", "password", "num_pools", "headers", "connection_pool_kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "urllib3.contrib.socks.SOCKSProxyManager.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 4], "arg_names": ["self", "proxy_url", "username", "password", "num_pools", "headers", "connection_pool_kw"], "arg_types": ["urllib3.contrib.socks.SOCKSProxyManager", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of SOCKSProxyManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pool_classes_by_scheme": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "urllib3.contrib.socks.SOCKSProxyManager.pool_classes_by_scheme", "name": "pool_classes_by_scheme", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.type"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "proxy_url": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.contrib.socks.SOCKSProxyManager.proxy_url", "name": "proxy_url", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.contrib.socks.SOCKSProxyManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.contrib.socks.SOCKSProxyManager", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SocketTimeout": {".class": "SymbolTableNode", "cross_ref": "socket.timeout", "kind": "Gdef"}, "_TYPE_SOCKS_OPTIONS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.contrib.socks._TYPE_SOCKS_OPTIONS", "name": "_TYPE_SOCKS_OPTIONS", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "urllib3.contrib.socks._TYPE_SOCKS_OPTIONS", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.contrib.socks", "mro": ["urllib3.contrib.socks._TYPE_SOCKS_OPTIONS", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["socks_version", "builtins.int"], ["proxy_host", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["proxy_port", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["username", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["password", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["rdns", "builtins.bool"]], "readonly_keys": [], "required_keys": ["password", "proxy_host", "proxy_port", "rdns", "socks_version", "username"]}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.contrib.socks.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.contrib.socks.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.contrib.socks.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.contrib.socks.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.contrib.socks.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.contrib.socks.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "parse_url": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.url.parse_url", "kind": "Gdef"}, "socks": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "urllib3.contrib.socks.socks", "name": "socks", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "urllib3.contrib.socks.socks", "source_any": null, "type_of_any": 3}}}, "ssl": {".class": "SymbolTableNode", "cross_ref": "ssl", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\Documents\\GitHub\\workshop\\.conda\\Lib\\site-packages\\urllib3\\contrib\\socks.py"}