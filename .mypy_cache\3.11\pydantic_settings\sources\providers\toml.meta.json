{"data_mtime": 1752485752, "dep_lines": [12, 13, 16, 3, 5, 6, 7, 19, 22, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 25, 5, 10, 5, 5, 25, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic_settings.sources.base", "pydantic_settings.sources.types", "pydantic_settings.main", "__future__", "sys", "pathlib", "typing", "<PERSON><PERSON><PERSON><PERSON>", "to<PERSON>li", "builtins", "_frozen_importlib", "_typeshed", "abc", "os", "pydantic", "pydantic._internal", "pydantic._internal._generate_schema", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic.aliases", "pydantic.fields", "pydantic.main", "re"], "hash": "ca1d3a1b64f61c7b9c3ff3e6e1b397d45de34d2c", "id": "pydantic_settings.sources.providers.toml", "ignore_all": true, "interface_hash": "043388715dc98da3f724efed7f1c81c310bfc62e", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Documents\\GitHub\\workshop\\.conda\\Lib\\site-packages\\pydantic_settings\\sources\\providers\\toml.py", "plugin_data": null, "size": 1827, "suppressed": [], "version_id": "1.16.1"}