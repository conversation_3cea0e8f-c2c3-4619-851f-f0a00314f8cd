{"data_mtime": **********, "dep_lines": [5, 7, 8, 9, 10, 12, 21, 22, 23, 24, 5, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 5, 30, 30, 30], "dependencies": ["starlette.status", "fastapi.applications", "fastapi.background", "fastapi.datastructures", "fastapi.exceptions", "fastapi.param_functions", "fastapi.requests", "fastapi.responses", "fastapi.routing", "fastapi.websockets", "starlette", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "06f25b7663c5c51ad1ab23c5ef9edebc9ac4cec9", "id": "<PERSON><PERSON><PERSON>", "ignore_all": true, "interface_hash": "c881df67006e50e3500fb04ad09ce264a788d65a", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Documents\\GitHub\\workshop\\.conda\\Lib\\site-packages\\fastapi\\__init__.py", "plugin_data": null, "size": 1081, "suppressed": [], "version_id": "1.16.1"}