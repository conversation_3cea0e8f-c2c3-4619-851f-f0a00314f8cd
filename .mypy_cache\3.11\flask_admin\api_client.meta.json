{"data_mtime": 1752486191, "dep_lines": [7, 5, 6, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["flask_admin.config", "requests", "typing", "builtins", "_frozen_importlib", "_typeshed", "abc", "http", "http.cookiejar", "json", "json.decoder", "requests.auth", "requests.cookies", "requests.models", "requests.sessions", "typing_extensions"], "hash": "46ae107daf50c8943cf1709bf46c5209acd017ce", "id": "flask_admin.api_client", "ignore_all": false, "interface_hash": "54def4235503d427b0d5f7dd0152030c3c38e7a7", "mtime": 1752485591, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": ".\\flask_admin\\api_client.py", "plugin_data": null, "size": 4423, "suppressed": [], "version_id": "1.16.1"}