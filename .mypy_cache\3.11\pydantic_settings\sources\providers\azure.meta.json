{"data_mtime": **********, "dep_lines": [10, 5, 8, 17, 3, 6, 1, 1, 1, 1, 1, 1, 1, 1, 1, 13, 14, 15], "dep_prios": [5, 5, 5, 25, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 25, 25, 25], "dependencies": ["pydantic_settings.sources.providers.env", "collections.abc", "pydantic.fields", "pydantic_settings.main", "__future__", "typing", "builtins", "_frozen_importlib", "abc", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic.main", "pydantic_settings.sources.base"], "hash": "cf36a47f3cb79b6e59ef13d32d45dc2f32305e6c", "id": "pydantic_settings.sources.providers.azure", "ignore_all": true, "interface_hash": "f7a95532125c6ca5f7e385d6f6063957c9fb2b2d", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Documents\\GitHub\\workshop\\.conda\\Lib\\site-packages\\pydantic_settings\\sources\\providers\\azure.py", "plugin_data": null, "size": 4108, "suppressed": ["azure.core.credentials", "azure.core.exceptions", "azure.keyvault.secrets"], "version_id": "1.16.1"}