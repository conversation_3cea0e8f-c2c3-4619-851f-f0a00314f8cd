{".class": "MypyFile", "_fullname": "pydantic._internal._fields", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AnnotationSource": {".class": "SymbolTableNode", "cross_ref": "typing_inspection.introspection.AnnotationSource", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BaseMetadata": {".class": "SymbolTableNode", "cross_ref": "annotated_types.BaseMetadata", "kind": "Gdef"}, "BaseModel": {".class": "SymbolTableNode", "cross_ref": "pydantic.main.BaseModel", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "ConfigWrapper": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._config.ConfigWrapper", "kind": "Gdef"}, "DecoratorInfos": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._decorators.DecoratorInfos", "kind": "Gdef"}, "FieldInfo": {".class": "SymbolTableNode", "cross_ref": "pydantic.fields.FieldInfo", "kind": "Gdef"}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "NsResolver": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._namespace_utils.NsResolver", "kind": "Gdef"}, "Parameter": {".class": "SymbolTableNode", "cross_ref": "inspect.Parameter", "kind": "Gdef"}, "Pattern": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON>", "kind": "Gdef"}, "PydanticDataclass": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._dataclasses.PydanticDataclass", "kind": "Gdef"}, "PydanticDeprecatedSince211": {".class": "SymbolTableNode", "cross_ref": "pydantic.warnings.PydanticDeprecatedSince211", "kind": "Gdef"}, "PydanticMetadata": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pydantic._internal._repr.Representation"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic._internal._fields.PydanticMetadata", "name": "PydanticMetadata", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic._internal._fields.PydanticMetadata", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic._internal._fields", "mro": ["pydantic._internal._fields.PydanticMetadata", "pydantic._internal._repr.Representation", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "pydantic._internal._fields.PydanticMetadata.__slots__", "name": "__slots__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._fields.PydanticMetadata.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic._internal._fields.PydanticMetadata", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PydanticUndefined": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.PydanticUndefined", "kind": "Gdef"}, "PydanticUserError": {".class": "SymbolTableNode", "cross_ref": "pydantic.errors.PydanticUserError", "kind": "Gdef"}, "Representation": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._repr.Representation", "kind": "Gdef"}, "StandardDataclass": {".class": "SymbolTableNode", "cross_ref": "_typeshed.DataclassInstance", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TypeIs": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.TypeIs", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._fields.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._fields.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._fields.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._fields.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._fields.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._fields.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "_general_metadata_cls": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "pydantic._internal._fields._general_metadata_cls", "name": "_general_metadata_cls", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_general_metadata_cls", "ret_type": {".class": "TypeType", "item": "annotated_types.BaseMetadata"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "pydantic._internal._fields._general_metadata_cls", "name": "_general_metadata_cls", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TypeType", "item": "annotated_types.BaseMetadata"}], "extra_attrs": null, "type_ref": "functools._lru_cache_wrapper"}}}}, "_generics": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._generics", "kind": "Gdef"}, "_typing_extra": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._typing_extra", "kind": "Gdef"}, "_update_fields_from_docstrings": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["cls", "fields", "use_inspect"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._fields._update_fields_from_docstrings", "name": "_update_fields_from_docstrings", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "fields", "use_inspect"], "arg_types": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {".class": "Instance", "args": ["builtins.str", "pydantic.fields.FieldInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_update_fields_from_docstrings", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_warn_on_nested_alias_in_annotation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["ann_type", "ann_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._fields._warn_on_nested_alias_in_annotation", "name": "_warn_on_nested_alias_in_annotation", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["ann_type", "ann_name"], "arg_types": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_warn_on_nested_alias_in_annotation", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cache": {".class": "SymbolTableNode", "cross_ref": "functools.cache", "kind": "Gdef"}, "can_be_positional": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._utils.can_be_positional", "kind": "Gdef"}, "collect_dataclass_fields": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5], "arg_names": ["cls", "ns_resolver", "typevars_map", "config_wrapper"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._fields.collect_dataclass_fields", "name": "collect_dataclass_fields", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5], "arg_names": ["cls", "ns_resolver", "typevars_map", "config_wrapper"], "arg_types": [{".class": "TypeType", "item": "_typeshed.DataclassInstance"}, {".class": "UnionType", "items": ["pydantic._internal._namespace_utils.NsResolver", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["pydantic._internal._config.ConfigWrapper", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "collect_dataclass_fields", "ret_type": {".class": "Instance", "args": ["builtins.str", "pydantic.fields.FieldInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "collect_model_fields": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5], "arg_names": ["cls", "config_wrapper", "ns_resolver", "typevars_map"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._fields.collect_model_fields", "name": "collect_model_fields", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5], "arg_names": ["cls", "config_wrapper", "ns_resolver", "typevars_map"], "arg_types": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}, "pydantic._internal._config.ConfigWrapper", {".class": "UnionType", "items": ["pydantic._internal._namespace_utils.NsResolver", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["typing.TypeVar", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "collect_model_fields", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str", "pydantic.fields.FieldInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "copy": {".class": "SymbolTableNode", "cross_ref": "copy.copy", "kind": "Gdef"}, "dataclasses": {".class": "SymbolTableNode", "cross_ref": "dataclasses", "kind": "Gdef"}, "extract_docstrings_from_cls": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._docs_extraction.extract_docstrings_from_cls", "kind": "Gdef"}, "get_origin": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.get_origin", "kind": "Gdef"}, "import_cached_base_model": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._import_utils.import_cached_base_model", "kind": "Gdef"}, "import_cached_field_info": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._import_utils.import_cached_field_info", "kind": "Gdef"}, "is_valid_field_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._fields.is_valid_field_name", "name": "is_valid_field_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["name"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_valid_field_name", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_valid_privateattr_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._fields.is_valid_privateattr_name", "name": "is_valid_privateattr_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["name"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_valid_privateattr_name", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ismethoddescriptor": {".class": "SymbolTableNode", "cross_ref": "inspect.ismethoddescriptor", "kind": "Gdef"}, "pydantic_general_metadata": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [4], "arg_names": ["metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._fields.pydantic_general_metadata", "name": "pydantic_general_metadata", "type": {".class": "CallableType", "arg_kinds": [4], "arg_names": ["metadata"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pydantic_general_metadata", "ret_type": "annotated_types.BaseMetadata", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rebuild_dataclass_fields": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 3], "arg_names": ["cls", "config_wrapper", "ns_resolver", "typevars_map"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._fields.rebuild_dataclass_fields", "name": "rebuild_dataclass_fields", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3], "arg_names": ["cls", "config_wrapper", "ns_resolver", "typevars_map"], "arg_types": [{".class": "TypeType", "item": "pydantic._internal._dataclasses.PydanticDataclass"}, "pydantic._internal._config.ConfigWrapper", "pydantic._internal._namespace_utils.NsResolver", {".class": "Instance", "args": ["typing.TypeVar", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "rebuild_dataclass_fields", "ret_type": {".class": "Instance", "args": ["builtins.str", "pydantic.fields.FieldInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rebuild_model_fields": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3], "arg_names": ["cls", "ns_resolver", "typevars_map"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._fields.rebuild_model_fields", "name": "rebuild_model_fields", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3], "arg_names": ["cls", "ns_resolver", "typevars_map"], "arg_types": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}, "pydantic._internal._namespace_utils.NsResolver", {".class": "Instance", "args": ["typing.TypeVar", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "rebuild_model_fields", "ret_type": {".class": "Instance", "args": ["builtins.str", "pydantic.fields.FieldInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "signature": {".class": "SymbolTableNode", "cross_ref": "inspect.signature", "kind": "Gdef"}, "takes_validated_data_argument": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["default_factory"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._fields.takes_validated_data_argument", "name": "takes_validated_data_argument", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["default_factory"], "arg_types": [{".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "takes_validated_data_argument", "ret_type": "builtins.bool", "type_guard": null, "type_is": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "unpack_kwargs": false, "variables": []}}}, "typing_objects": {".class": "SymbolTableNode", "cross_ref": "typing_inspection.typing_objects", "kind": "Gdef"}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\Documents\\GitHub\\workshop\\.conda\\Lib\\site-packages\\pydantic\\_internal\\_fields.py"}