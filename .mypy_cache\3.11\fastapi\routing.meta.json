{"data_mtime": 1752485753, "dep_lines": [34, 35, 3, 24, 25, 33, 44, 45, 51, 52, 60, 61, 62, 63, 64, 74, 75, 1, 2, 3, 4, 5, 6, 7, 8, 24, 59, 60, 76, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 20, 10, 10, 5, 5, 5, 20, 5, 20, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["fastapi.dependencies.models", "fastapi.dependencies.utils", "email.message", "fastapi.params", "fastapi._compat", "fastapi.datastructures", "fastapi.encoders", "fastapi.exceptions", "fastapi.types", "fastapi.utils", "starlette.routing", "starlette.concurrency", "starlette.exceptions", "starlette.requests", "starlette.responses", "starlette.types", "starlette.websockets", "asyncio", "dataclasses", "email", "inspect", "json", "contextlib", "enum", "typing", "<PERSON><PERSON><PERSON>", "pydantic", "starlette", "typing_extensions", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "fastapi.dependencies", "pydantic._internal", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic._internal._utils", "pydantic.fields", "pydantic.main", "pydantic_core", "pydantic_core._pydantic_core", "re", "starlette.background", "starlette.convertors", "starlette.middleware", "types"], "hash": "9ab753216acb640cf46fa63301a9cd4f3f253b31", "id": "fastapi.routing", "ignore_all": true, "interface_hash": "f9c289b9f98a31f7aae505f9bbaad92e002506c8", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Documents\\GitHub\\workshop\\.conda\\Lib\\site-packages\\fastapi\\routing.py", "plugin_data": null, "size": 176315, "suppressed": [], "version_id": "1.16.1"}