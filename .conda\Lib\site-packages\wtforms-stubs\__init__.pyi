from typing import Final

from wtforms import validators as validators, widgets as widgets
from wtforms.fields.choices import (
    RadioField as <PERSON><PERSON>ield,
    SelectField as SelectField,
    SelectFieldBase as SelectFieldBase,
    SelectMultipleField as SelectMultipleField,
)
from wtforms.fields.core import Field as Field, Flags as Flags, Label as Label
from wtforms.fields.datetime import (
    Date<PERSON><PERSON> as Date<PERSON>ield,
    DateTime<PERSON>ield as Date<PERSON>ime<PERSON>ield,
    DateTimeLocalField as DateTimeLocalField,
    <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON>,
    Time<PERSON>ield as <PERSON><PERSON>ield,
    WeekField as <PERSON>Field,
)
from wtforms.fields.form import FormField as FormField
from wtforms.fields.list import Field<PERSON>ist as FieldList
from wtforms.fields.numeric import (
    DecimalField as Decimal<PERSON>ield,
    DecimalRangeField as DecimalRangeField,
    <PERSON><PERSON><PERSON><PERSON> as <PERSON>loat<PERSON>ield,
    <PERSON><PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
    IntegerRange<PERSON><PERSON> as IntegerRange<PERSON>ield,
)
from wtforms.fields.simple import (
    <PERSON><PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>ield as <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>ield as <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON> as <PERSON><PERSON>ield,
    SubmitField as SubmitField,
    TelField as TelField,
    TextAreaField as TextAreaField,
    URLField as URLField,
)
from wtforms.form import Form as Form
from wtforms.validators import ValidationError as ValidationError

__version__: Final[str]
__all__ = [
    "validators",
    "widgets",
    "Form",
    "ValidationError",
    "SelectField",
    "SelectFieldBase",
    "SelectMultipleField",
    "RadioField",
    "Field",
    "Flags",
    "Label",
    "DateTimeField",
    "DateField",
    "TimeField",
    "MonthField",
    "DateTimeLocalField",
    "WeekField",
    "FormField",
    "FieldList",
    "IntegerField",
    "DecimalField",
    "FloatField",
    "IntegerRangeField",
    "DecimalRangeField",
    "BooleanField",
    "TextAreaField",
    "PasswordField",
    "FileField",
    "MultipleFileField",
    "HiddenField",
    "SearchField",
    "SubmitField",
    "StringField",
    "TelField",
    "URLField",
    "EmailField",
    "ColorField",
]
