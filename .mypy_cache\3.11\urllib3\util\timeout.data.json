{".class": "MypyFile", "_fullname": "urllib3.util.timeout", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef"}, "Final": {".class": "SymbolTableNode", "cross_ref": "typing.Final", "kind": "Gdef"}, "Timeout": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.util.timeout.Timeout", "name": "Timeout", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "urllib3.util.timeout.Timeout", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.util.timeout", "mro": ["urllib3.util.timeout.Timeout", "builtins.object"], "names": {".class": "SymbolTable", "DEFAULT_TIMEOUT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "urllib3.util.timeout.Timeout.DEFAULT_TIMEOUT", "name": "DEFAULT_TIMEOUT", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.timeout._TYPE_TIMEOUT"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "total", "connect", "read"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "urllib3.util.timeout.Timeout.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "total", "connect", "read"], "arg_types": ["urllib3.util.timeout.Timeout", {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.timeout._TYPE_TIMEOUT"}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.timeout._TYPE_TIMEOUT"}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.timeout._TYPE_TIMEOUT"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of Timeout", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "urllib3.util.timeout.Timeout.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["urllib3.util.timeout.Timeout"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__repr__ of Timeout", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "urllib3.util.timeout.Timeout.__str__", "name": "__str__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["urllib3.util.timeout.Timeout"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_connect": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.util.timeout.Timeout._connect", "name": "_connect", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.float", "urllib3.util.timeout._TYPE_DEFAULT", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_read": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.util.timeout.Timeout._read", "name": "_read", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.float", "urllib3.util.timeout._TYPE_DEFAULT", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_start_connect": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "urllib3.util.timeout.Timeout._start_connect", "name": "_start_connect", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_validate_timeout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "value", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "urllib3.util.timeout.Timeout._validate_timeout", "name": "_validate_timeout", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "value", "name"], "arg_types": [{".class": "TypeType", "item": "urllib3.util.timeout.Timeout"}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.timeout._TYPE_TIMEOUT"}, "builtins.str"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_validate_timeout of Timeout", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.timeout._TYPE_TIMEOUT"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "urllib3.util.timeout.Timeout._validate_timeout", "name": "_validate_timeout", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "value", "name"], "arg_types": [{".class": "TypeType", "item": "urllib3.util.timeout.Timeout"}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.timeout._TYPE_TIMEOUT"}, "builtins.str"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_validate_timeout of Timeout", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.timeout._TYPE_TIMEOUT"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "clone": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "urllib3.util.timeout.Timeout.clone", "name": "clone", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["urllib3.util.timeout.Timeout"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "clone of Timeout", "ret_type": "urllib3.util.timeout.Timeout", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "connect_timeout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "urllib3.util.timeout.Timeout.connect_timeout", "name": "connect_timeout", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["urllib3.util.timeout.Timeout"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "connect_timeout of Timeout", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.timeout._TYPE_TIMEOUT"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "urllib3.util.timeout.Timeout.connect_timeout", "name": "connect_timeout", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["urllib3.util.timeout.Timeout"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "connect_timeout of Timeout", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.timeout._TYPE_TIMEOUT"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_float": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "urllib3.util.timeout.Timeout.from_float", "name": "from_float", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "timeout"], "arg_types": [{".class": "TypeType", "item": "urllib3.util.timeout.Timeout"}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.timeout._TYPE_TIMEOUT"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_float of Timeout", "ret_type": "urllib3.util.timeout.Timeout", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "urllib3.util.timeout.Timeout.from_float", "name": "from_float", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "timeout"], "arg_types": [{".class": "TypeType", "item": "urllib3.util.timeout.Timeout"}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.timeout._TYPE_TIMEOUT"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_float of Timeout", "ret_type": "urllib3.util.timeout.Timeout", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_connect_duration": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "urllib3.util.timeout.Timeout.get_connect_duration", "name": "get_connect_duration", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["urllib3.util.timeout.Timeout"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_connect_duration of Timeout", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "read_timeout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "urllib3.util.timeout.Timeout.read_timeout", "name": "read_timeout", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["urllib3.util.timeout.Timeout"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "read_timeout of Timeout", "ret_type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "urllib3.util.timeout.Timeout.read_timeout", "name": "read_timeout", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["urllib3.util.timeout.Timeout"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "read_timeout of Timeout", "ret_type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "resolve_default_timeout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "urllib3.util.timeout.Timeout.resolve_default_timeout", "name": "resolve_default_timeout", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["timeout"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.timeout._TYPE_TIMEOUT"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "resolve_default_timeout of Timeout", "ret_type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "urllib3.util.timeout.Timeout.resolve_default_timeout", "name": "resolve_default_timeout", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["timeout"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.timeout._TYPE_TIMEOUT"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "resolve_default_timeout of Timeout", "ret_type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "start_connect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "urllib3.util.timeout.Timeout.start_connect", "name": "start_connect", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["urllib3.util.timeout.Timeout"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "start_connect of Timeout", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "total": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.util.timeout.Timeout.total", "name": "total", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.float", "urllib3.util.timeout._TYPE_DEFAULT", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.util.timeout.Timeout.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.util.timeout.Timeout", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TimeoutStateError": {".class": "SymbolTableNode", "cross_ref": "urllib3.exceptions.TimeoutStateError", "kind": "Gdef"}, "_DEFAULT_TIMEOUT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready", "has_explicit_value"], "fullname": "urllib3.util.timeout._DEFAULT_TIMEOUT", "name": "_DEFAULT_TIMEOUT", "setter_type": null, "type": "urllib3.util.timeout._TYPE_DEFAULT"}}, "_TYPE_DEFAULT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.util.timeout._TYPE_DEFAULT", "name": "_TYPE_DEFAULT", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "urllib3.util.timeout._TYPE_DEFAULT", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "urllib3.util.timeout", "mro": ["urllib3.util.timeout._TYPE_DEFAULT", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "urllib3.util.timeout._TYPE_DEFAULT.token", "name": "token", "setter_type": null, "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": -1}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.util.timeout._TYPE_DEFAULT.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.util.timeout._TYPE_DEFAULT", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_TYPE_TIMEOUT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "urllib3.util.timeout._TYPE_TIMEOUT", "line": 22, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["builtins.float", "urllib3.util.timeout._TYPE_DEFAULT", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.util.timeout.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.util.timeout.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.util.timeout.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.util.timeout.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.util.timeout.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.util.timeout.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "getdefaulttimeout": {".class": "SymbolTableNode", "cross_ref": "_socket.getdefaulttimeout", "kind": "Gdef"}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\Documents\\GitHub\\workshop\\.conda\\Lib\\site-packages\\urllib3\\util\\timeout.py"}