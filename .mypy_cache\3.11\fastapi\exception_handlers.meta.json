{"data_mtime": 1752485753, "dep_lines": [1, 2, 3, 4, 5, 6, 7, 8, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["fastapi.encoders", "fastapi.exceptions", "fastapi.utils", "fastapi.websockets", "starlette.exceptions", "starlette.requests", "starlette.responses", "starlette.status", "builtins", "_frozen_importlib", "abc", "starlette", "starlette.websockets", "typing"], "hash": "37c9f31237b8884ebb07dc2f2eb8a69c3737577e", "id": "fastapi.exception_handlers", "ignore_all": true, "interface_hash": "06f335f5790a02653520a2e042129358277a451b", "mtime": 1752117067, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Documents\\GitHub\\workshop\\.conda\\Lib\\site-packages\\fastapi\\exception_handlers.py", "plugin_data": null, "size": 1332, "suppressed": [], "version_id": "1.16.1"}