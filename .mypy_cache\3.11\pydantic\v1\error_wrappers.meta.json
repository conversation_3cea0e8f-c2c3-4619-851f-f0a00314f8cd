{"data_mtime": 1752485749, "dep_lines": [4, 5, 10, 11, 12, 1, 2, 8, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 25, 25, 25, 10, 5, 25, 5, 30, 30, 30, 30], "dependencies": ["pydantic.v1.json", "pydantic.v1.utils", "pydantic.v1.config", "pydantic.v1.types", "pydantic.v1.typing", "json", "typing", "typing_extensions", "builtins", "_frozen_importlib", "abc", "pydantic.v1.dataclasses", "pydantic.v1.main"], "hash": "5ba402b9ba6723626e62ccb4a225b352e9b91602", "id": "pydantic.v1.error_wrappers", "ignore_all": true, "interface_hash": "d2dd2543222ea45b083ca4c4e2bf150e8f6b7bc0", "mtime": 1749927242, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Documents\\GitHub\\workshop\\.conda\\Lib\\site-packages\\pydantic\\v1\\error_wrappers.py", "plugin_data": null, "size": 5196, "suppressed": [], "version_id": "1.16.1"}