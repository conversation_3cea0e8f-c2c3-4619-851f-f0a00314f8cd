{"data_mtime": 1752485751, "dep_lines": [9, 10, 11, 1, 3, 4, 5, 7, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 10, 5, 5, 5, 30, 30], "dependencies": ["pydantic.json_schema", "pydantic.type_adapter", "pydantic.warnings", "__future__", "json", "warnings", "typing", "typing_extensions", "builtins", "_frozen_importlib", "abc"], "hash": "97d986de9165a54fc5a9e0ca37bfe34a496fd7bc", "id": "pydantic.deprecated.tools", "ignore_all": true, "interface_hash": "f937c55f81f66cfe627af68449512d851812d6f6", "mtime": 1749927242, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Documents\\GitHub\\workshop\\.conda\\Lib\\site-packages\\pydantic\\deprecated\\tools.py", "plugin_data": null, "size": 3330, "suppressed": [], "version_id": "1.16.1"}