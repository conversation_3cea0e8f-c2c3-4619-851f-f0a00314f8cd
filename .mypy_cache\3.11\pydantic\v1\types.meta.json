{"data_mtime": 1752485749, "dep_lines": [31, 32, 33, 34, 126, 127, 128, 31, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 28, 29, 124, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 25, 25, 25, 20, 10, 10, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 25, 5, 30, 30, 30, 30, 30], "dependencies": ["pydantic.v1.errors", "pydantic.v1.datetime_parse", "pydantic.v1.utils", "pydantic.v1.validators", "pydantic.v1.dataclasses", "pydantic.v1.main", "pydantic.v1.typing", "pydantic.v1", "abc", "math", "re", "warnings", "datetime", "decimal", "enum", "pathlib", "types", "typing", "uuid", "weakref", "typing_extensions", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "_weakrefset", "os"], "hash": "cb14abb9f271408deb698b683272224c4c009a70", "id": "pydantic.v1.types", "ignore_all": true, "interface_hash": "e4a8f1ace562deb80bac5fd9a624a1c45c433f14", "mtime": 1749927242, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Documents\\GitHub\\workshop\\.conda\\Lib\\site-packages\\pydantic\\v1\\types.py", "plugin_data": null, "size": 35455, "suppressed": [], "version_id": "1.16.1"}