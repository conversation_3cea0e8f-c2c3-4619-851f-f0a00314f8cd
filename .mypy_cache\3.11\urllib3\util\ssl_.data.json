{".class": "MypyFile", "_fullname": "urllib3.util.ssl_", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ALPN_PROTOCOLS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "urllib3.util.ssl_.ALPN_PROTOCOLS", "name": "ALPN_PROTOCOLS", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "CERT_REQUIRED": {".class": "SymbolTableNode", "cross_ref": "ssl.CERT_REQUIRED", "kind": "Gdef"}, "HASHFUNC_MAP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "urllib3.util.ssl_.HASHFUNC_MAP", "name": "HASHFUNC_MAP", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.int", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "HAS_NEVER_CHECK_COMMON_NAME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "urllib3.util.ssl_.HAS_NEVER_CHECK_COMMON_NAME", "name": "HAS_NEVER_CHECK_COMMON_NAME", "setter_type": null, "type": "builtins.bool"}}, "IS_PYOPENSSL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "urllib3.util.ssl_.IS_PYOPENSSL", "name": "IS_PYOPENSSL", "setter_type": null, "type": "builtins.bool"}}, "OPENSSL_VERSION": {".class": "SymbolTableNode", "cross_ref": "_ssl.OPENSSL_VERSION", "kind": "Gdef"}, "OPENSSL_VERSION_NUMBER": {".class": "SymbolTableNode", "cross_ref": "_ssl.OPENSSL_VERSION_NUMBER", "kind": "Gdef"}, "OP_NO_COMPRESSION": {".class": "SymbolTableNode", "cross_ref": "ssl.OP_NO_COMPRESSION", "kind": "Gdef"}, "OP_NO_SSLv2": {".class": "SymbolTableNode", "cross_ref": "ssl.OP_NO_SSLv2", "kind": "Gdef"}, "OP_NO_SSLv3": {".class": "SymbolTableNode", "cross_ref": "ssl.OP_NO_SSLv3", "kind": "Gdef"}, "OP_NO_TICKET": {".class": "SymbolTableNode", "cross_ref": "ssl.OP_NO_TICKET", "kind": "Gdef"}, "PROTOCOL_SSLv23": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "urllib3.util.ssl_.PROTOCOL_SSLv23", "name": "PROTOCOL_SSLv23", "setter_type": null, "type": "ssl._SSLMethod"}}, "PROTOCOL_TLS": {".class": "SymbolTableNode", "cross_ref": "ssl.PROTOCOL_TLS", "kind": "Gdef"}, "PROTOCOL_TLS_CLIENT": {".class": "SymbolTableNode", "cross_ref": "ssl.PROTOCOL_TLS_CLIENT", "kind": "Gdef"}, "ProxySchemeUnsupported": {".class": "SymbolTableNode", "cross_ref": "urllib3.exceptions.ProxySchemeUnsupported", "kind": "Gdef"}, "SSLContext": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "urllib3.util.ssl_.SSLContext", "name": "SSLContext", "setter_type": null, "type": {".class": "NoneType"}}}, "SSLError": {".class": "SymbolTableNode", "cross_ref": "urllib3.exceptions.SSLError", "kind": "Gdef"}, "SSLTransport": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "urllib3.util.ssl_.SSLTransport", "name": "SSLTransport", "setter_type": null, "type": {".class": "NoneType"}}}, "SSLTransportType": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.ssltransport.SSLTransport", "kind": "Gdef"}, "TLSVersion": {".class": "SymbolTableNode", "cross_ref": "ssl.TLSVersion", "kind": "Gdef"}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef"}, "VERIFY_X509_PARTIAL_CHAIN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "urllib3.util.ssl_.VERIFY_X509_PARTIAL_CHAIN", "name": "VERIFY_X509_PARTIAL_CHAIN", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.int"], "uses_pep604_syntax": false}}}, "VERIFY_X509_STRICT": {".class": "SymbolTableNode", "cross_ref": "ssl.VERIFY_X509_STRICT", "kind": "Gdef"}, "VerifyMode": {".class": "SymbolTableNode", "cross_ref": "ssl.VerifyMode", "kind": "Gdef"}, "_BRACELESS_IPV6_ADDRZ_RE": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.url._BRACELESS_IPV6_ADDRZ_RE", "kind": "Gdef"}, "_IPV4_RE": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.url._IPV4_RE", "kind": "Gdef"}, "_SSL_VERSION_TO_TLS_VERSION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "urllib3.util.ssl_._SSL_VERSION_TO_TLS_VERSION", "name": "_SSL_VERSION_TO_TLS_VERSION", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.int", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_TYPE_PEER_CERT_RET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "urllib3.util.ssl_._TYPE_PEER_CERT_RET", "line": 149, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.ssl_._TYPE_PEER_CERT_RET_DICT"}, "builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_TYPE_PEER_CERT_RET_DICT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.util.ssl_._TYPE_PEER_CERT_RET_DICT", "name": "_TYPE_PEER_CERT_RET_DICT", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "urllib3.util.ssl_._TYPE_PEER_CERT_RET_DICT", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.util.ssl_", "mro": ["urllib3.util.ssl_._TYPE_PEER_CERT_RET_DICT", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["subjectAltName", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.tuple"}], ["subject", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "extra_attrs": null, "type_ref": "builtins.tuple"}], ["serialNumber", "builtins.str"]], "readonly_keys": [], "required_keys": []}}}, "_TYPE_VERSION_INFO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "urllib3.util.ssl_._TYPE_VERSION_INFO", "line": 21, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int", "builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.util.ssl_.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.util.ssl_.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.util.ssl_.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.util.ssl_.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.util.ssl_.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.util.ssl_.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_is_bpo_43522_fixed": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["implementation_name", "version_info", "pypy_version_info"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.util.ssl_._is_bpo_43522_fixed", "name": "_is_bpo_43522_fixed", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["implementation_name", "version_info", "pypy_version_info"], "arg_types": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.ssl_._TYPE_VERSION_INFO"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.ssl_._TYPE_VERSION_INFO"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_is_bpo_43522_fixed", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_is_has_never_check_common_name_reliable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["openssl_version", "openssl_version_number", "implementation_name", "version_info", "pypy_version_info"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.util.ssl_._is_has_never_check_common_name_reliable", "name": "_is_has_never_check_common_name_reliable", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["openssl_version", "openssl_version_number", "implementation_name", "version_info", "pypy_version_info"], "arg_types": ["builtins.str", "builtins.int", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.ssl_._TYPE_VERSION_INFO"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.ssl_._TYPE_VERSION_INFO"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_is_has_never_check_common_name_reliable", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_is_key_file_encrypted": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["key_file"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.util.ssl_._is_key_file_encrypted", "name": "_is_key_file_encrypted", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["key_file"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_is_key_file_encrypted", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_ssl_wrap_socket_impl": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["sock", "ssl_context", "tls_in_tls", "server_hostname"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.util.ssl_._ssl_wrap_socket_impl", "name": "_ssl_wrap_socket_impl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["sock", "ssl_context", "tls_in_tls", "server_hostname"], "arg_types": ["socket.socket", "ssl.SSLContext", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_ssl_wrap_socket_impl", "ret_type": {".class": "UnionType", "items": ["ssl.SSLSocket", "urllib3.util.ssltransport.SSLTransport"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "assert_fingerprint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cert", "fingerprint"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.util.ssl_.assert_fingerprint", "name": "assert_fingerprint", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cert", "fingerprint"], "arg_types": [{".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "assert_fingerprint", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "attr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_index_var", "is_inferred"], "fullname": "urllib3.util.ssl_.attr", "name": "attr", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "TLSv1"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "TLSv1_1"}, "type_ref": "builtins.str"}, {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "TLSv1_2"}, "type_ref": "builtins.str"}], "uses_pep604_syntax": false}}}, "create_urllib3_context": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 1, 1, 1, 1, 1], "arg_names": ["ssl_version", "cert_reqs", "options", "ciphers", "ssl_minimum_version", "ssl_maximum_version", "verify_flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.util.ssl_.create_urllib3_context", "name": "create_urllib3_context", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1, 1, 1, 1], "arg_names": ["ssl_version", "cert_reqs", "options", "ciphers", "ssl_minimum_version", "ssl_maximum_version", "verify_flags"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_urllib3_context", "ret_type": "ssl.SSLContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "hashlib": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "hmac": {".class": "SymbolTableNode", "cross_ref": "hmac", "kind": "Gdef"}, "is_ipaddress": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["hostname"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.util.ssl_.is_ipaddress", "name": "is_ipaddress", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["hostname"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_ipaddress", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "resolve_cert_reqs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["candidate"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.util.ssl_.resolve_cert_reqs", "name": "resolve_cert_reqs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["candidate"], "arg_types": [{".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int", "builtins.str"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "resolve_cert_reqs", "ret_type": "ssl.VerifyMode", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "resolve_ssl_version": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["candidate"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.util.ssl_.resolve_ssl_version", "name": "resolve_ssl_version", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["candidate"], "arg_types": [{".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.int", "builtins.str"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "resolve_ssl_version", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "socket": {".class": "SymbolTableNode", "cross_ref": "socket", "kind": "Gdef"}, "ssl": {".class": "SymbolTableNode", "cross_ref": "ssl", "kind": "Gdef"}, "ssl_wrap_socket": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "urllib3.util.ssl_.ssl_wrap_socket", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["sock", "keyfile", "certfile", "cert_reqs", "ca_certs", "server_hostname", "ssl_version", "ciphers", "ssl_context", "ca_cert_dir", "key_password", "ca_cert_data", "tls_in_tls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "urllib3.util.ssl_.ssl_wrap_socket", "name": "ssl_wrap_socket", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["sock", "keyfile", "certfile", "cert_reqs", "ca_certs", "server_hostname", "ssl_version", "ciphers", "ssl_context", "ca_cert_dir", "key_password", "ca_cert_data", "tls_in_tls"], "arg_types": ["socket.socket", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "ssl_wrap_socket", "ret_type": {".class": "UnionType", "items": ["ssl.SSLSocket", "urllib3.util.ssltransport.SSLTransport"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["sock", "keyfile", "certfile", "cert_reqs", "ca_certs", "server_hostname", "ssl_version", "ciphers", "ssl_context", "ca_cert_dir", "key_password", "ca_cert_data", "tls_in_tls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "urllib3.util.ssl_.ssl_wrap_socket", "name": "ssl_wrap_socket", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["sock", "keyfile", "certfile", "cert_reqs", "ca_certs", "server_hostname", "ssl_version", "ciphers", "ssl_context", "ca_cert_dir", "key_password", "ca_cert_data", "tls_in_tls"], "arg_types": ["socket.socket", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "ssl_wrap_socket", "ret_type": "ssl.SSLSocket", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "urllib3.util.ssl_.ssl_wrap_socket", "name": "ssl_wrap_socket", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["sock", "keyfile", "certfile", "cert_reqs", "ca_certs", "server_hostname", "ssl_version", "ciphers", "ssl_context", "ca_cert_dir", "key_password", "ca_cert_data", "tls_in_tls"], "arg_types": ["socket.socket", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "ssl_wrap_socket", "ret_type": "ssl.SSLSocket", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["sock", "keyfile", "certfile", "cert_reqs", "ca_certs", "server_hostname", "ssl_version", "ciphers", "ssl_context", "ca_cert_dir", "key_password", "ca_cert_data", "tls_in_tls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "urllib3.util.ssl_.ssl_wrap_socket", "name": "ssl_wrap_socket", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["sock", "keyfile", "certfile", "cert_reqs", "ca_certs", "server_hostname", "ssl_version", "ciphers", "ssl_context", "ca_cert_dir", "key_password", "ca_cert_data", "tls_in_tls"], "arg_types": ["socket.socket", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "ssl_wrap_socket", "ret_type": {".class": "UnionType", "items": ["ssl.SSLSocket", "urllib3.util.ssltransport.SSLTransport"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "urllib3.util.ssl_.ssl_wrap_socket", "name": "ssl_wrap_socket", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["sock", "keyfile", "certfile", "cert_reqs", "ca_certs", "server_hostname", "ssl_version", "ciphers", "ssl_context", "ca_cert_dir", "key_password", "ca_cert_data", "tls_in_tls"], "arg_types": ["socket.socket", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "ssl_wrap_socket", "ret_type": {".class": "UnionType", "items": ["ssl.SSLSocket", "urllib3.util.ssltransport.SSLTransport"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["sock", "keyfile", "certfile", "cert_reqs", "ca_certs", "server_hostname", "ssl_version", "ciphers", "ssl_context", "ca_cert_dir", "key_password", "ca_cert_data", "tls_in_tls"], "arg_types": ["socket.socket", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "ssl_wrap_socket", "ret_type": "ssl.SSLSocket", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["sock", "keyfile", "certfile", "cert_reqs", "ca_certs", "server_hostname", "ssl_version", "ciphers", "ssl_context", "ca_cert_dir", "key_password", "ca_cert_data", "tls_in_tls"], "arg_types": ["socket.socket", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "ssl_wrap_socket", "ret_type": {".class": "UnionType", "items": ["ssl.SSLSocket", "urllib3.util.ssltransport.SSLTransport"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "unhexlify": {".class": "SymbolTableNode", "cross_ref": "binascii.unhexlify", "kind": "Gdef"}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\Documents\\GitHub\\workshop\\.conda\\Lib\\site-packages\\urllib3\\util\\ssl_.py"}