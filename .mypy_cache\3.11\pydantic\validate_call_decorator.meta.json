{"data_mtime": 1752485751, "dep_lines": [10, 10, 10, 10, 11, 16, 3, 5, 6, 7, 8, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 20, 5, 25, 5, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._generate_schema", "pydantic._internal._typing_extra", "pydantic._internal._validate_call", "pydantic._internal", "pydantic.errors", "pydantic.config", "__future__", "inspect", "functools", "types", "typing", "builtins", "_frozen_importlib", "abc", "pydantic._internal._repr", "pydantic.aliases", "pydantic.fields", "re"], "hash": "a0b5758f9fd17928b80b9cc6f14783fb75313e6b", "id": "pydantic.validate_call_decorator", "ignore_all": true, "interface_hash": "dad435cdf3be77904c65ebdc5808339410b63252", "mtime": 1749927242, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Documents\\GitHub\\workshop\\.conda\\Lib\\site-packages\\pydantic\\validate_call_decorator.py", "plugin_data": null, "size": 4389, "suppressed": [], "version_id": "1.16.1"}