{"data_mtime": 1752485749, "dep_lines": [5, 6, 7, 8, 9, 4, 1, 2, 62, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic.v1.config", "pydantic.v1.errors", "pydantic.v1.main", "pydantic.v1.typing", "pydantic.v1.utils", "pydantic.v1", "functools", "typing", "inspect", "builtins", "_frozen_importlib", "_typeshed", "abc", "enum", "pydantic.v1.class_validators", "types", "typing_extensions"], "hash": "4dfef47d26f48e39812a607c878f47471e037aab", "id": "pydantic.v1.decorator", "ignore_all": true, "interface_hash": "4344dfcfb27df10af968f0a1a68ed2ca88d0163e", "mtime": 1749927242, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Documents\\GitHub\\workshop\\.conda\\Lib\\site-packages\\pydantic\\v1\\decorator.py", "plugin_data": null, "size": 10339, "suppressed": [], "version_id": "1.16.1"}