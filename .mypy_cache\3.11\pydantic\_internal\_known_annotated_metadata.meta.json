{"data_mtime": 1752485751, "dep_lines": [12, 13, 4, 10, 1, 3, 5, 6, 7, 9, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["pydantic._internal._fields", "pydantic._internal._import_utils", "collections.abc", "pydantic_core.core_schema", "__future__", "collections", "copy", "functools", "typing", "pydantic_core", "builtins", "_frozen_importlib", "_typeshed", "abc"], "hash": "4694f3cdd6b7b6e374305d217857cd4ed68afb6b", "id": "pydantic._internal._known_annotated_metadata", "ignore_all": true, "interface_hash": "6303f3a308827781af8e3f94058201e6369cf809", "mtime": 1749927242, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Documents\\GitHub\\workshop\\.conda\\Lib\\site-packages\\pydantic\\_internal\\_known_annotated_metadata.py", "plugin_data": null, "size": 16213, "suppressed": [], "version_id": "1.16.1"}