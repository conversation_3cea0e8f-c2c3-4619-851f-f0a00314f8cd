{".class": "MypyFile", "_fullname": "wtforms.fields.form", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BaseForm": {".class": "SymbolTableNode", "cross_ref": "wtforms.form.BaseForm", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DefaultMeta": {".class": "SymbolTableNode", "cross_ref": "wtforms.meta.DefaultMeta", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Field": {".class": "SymbolTableNode", "cross_ref": "wtforms.fields.core.Field", "kind": "Gdef", "module_hidden": true, "module_public": false}, "FormField": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["wtforms.fields.core.Field"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "wtforms.fields.form.FormField", "name": "FormField", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.form._BoundFormT", "id": 1, "name": "_BoundFormT", "namespace": "wtforms.fields.form.FormField", "upper_bound": "wtforms.form.BaseForm", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "wtforms.fields.form.FormField", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "wtforms.fields.form", "mro": ["wtforms.fields.form.FormField", "wtforms.fields.core.Field", "builtins.object"], "names": {".class": "SymbolTable", "__getattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "wtforms.fields.form.FormField.__getattr__", "name": "__getattr__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.form._BoundFormT", "id": 1, "name": "_BoundFormT", "namespace": "wtforms.fields.form.FormField", "upper_bound": "wtforms.form.BaseForm", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "wtforms.fields.form.FormField"}, "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__getattr__ of FormField", "ret_type": "wtforms.fields.core.Field", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "wtforms.fields.form.FormField.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.form._BoundFormT", "id": 1, "name": "_BoundFormT", "namespace": "wtforms.fields.form.FormField", "upper_bound": "wtforms.form.BaseForm", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "wtforms.fields.form.FormField"}, "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__getitem__ of FormField", "ret_type": "wtforms.fields.core.Field", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "form_class", "label", "validators", "separator", "description", "id", "default", "widget", "render_kw", "name", "_form", "_prefix", "_translations", "_meta"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "wtforms.fields.form.FormField.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "form_class", "label", "validators", "separator", "description", "id", "default", "widget", "render_kw", "name", "_form", "_prefix", "_translations", "_meta"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.form._BoundFormT", "id": 1, "name": "_BoundFormT", "namespace": "wtforms.fields.form.FormField", "upper_bound": "wtforms.form.BaseForm", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "wtforms.fields.form.FormField"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.form._BoundFormT", "id": 1, "name": "_BoundFormT", "namespace": "wtforms.fields.form.FormField", "upper_bound": "wtforms.form.BaseForm", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "NoneType"}, "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.form._BoundFormT", "id": 1, "name": "_BoundFormT", "namespace": "wtforms.fields.form.FormField", "upper_bound": "wtforms.form.BaseForm", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "wtforms.fields.form.FormField"}], "extra_attrs": null, "type_ref": "wtforms.fields.core._Widget"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["wtforms.form.BaseForm", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": ["wtforms.meta._SupportsGettextAndNgettext", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["wtforms.meta.DefaultMeta", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of FormField", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "wtforms.fields.form.FormField.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.form._BoundFormT", "id": 1, "name": "_BoundFormT", "namespace": "wtforms.fields.form.FormField", "upper_bound": "wtforms.form.BaseForm", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "wtforms.fields.form.FormField"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__iter__ of FormField", "ret_type": {".class": "Instance", "args": ["wtforms.fields.core.Field"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "wtforms.fields.form.FormField.data", "name": "data", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.form._BoundFormT", "id": 1, "name": "_BoundFormT", "namespace": "wtforms.fields.form.FormField", "upper_bound": "wtforms.form.BaseForm", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "wtforms.fields.form.FormField"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "data of FormField", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "wtforms.fields.form.FormField.data", "name": "data", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.form._BoundFormT", "id": 1, "name": "_BoundFormT", "namespace": "wtforms.fields.form.FormField", "upper_bound": "wtforms.form.BaseForm", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "wtforms.fields.form.FormField"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "data of FormField", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "errors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "wtforms.fields.form.FormField.errors", "name": "errors", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.form._BoundFormT", "id": 1, "name": "_BoundFormT", "namespace": "wtforms.fields.form.FormField", "upper_bound": "wtforms.form.BaseForm", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "wtforms.fields.form.FormField"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "errors of <PERSON><PERSON><PERSON>", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "wtforms.form._FormErrors"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "wtforms.fields.form.FormField.errors", "name": "errors", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.form._BoundFormT", "id": 1, "name": "_BoundFormT", "namespace": "wtforms.fields.form.FormField", "upper_bound": "wtforms.form.BaseForm", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "wtforms.fields.form.FormField"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "errors of <PERSON><PERSON><PERSON>", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "wtforms.form._FormErrors"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "form": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "wtforms.fields.form.FormField.form", "name": "form", "setter_type": null, "type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.form._BoundFormT", "id": 1, "name": "_BoundFormT", "namespace": "wtforms.fields.form.FormField", "upper_bound": "wtforms.form.BaseForm", "values": [], "variance": 0}}}, "form_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "wtforms.fields.form.FormField.form_class", "name": "form_class", "setter_type": null, "type": {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.form._BoundFormT", "id": 1, "name": "_BoundFormT", "namespace": "wtforms.fields.form.FormField", "upper_bound": "wtforms.form.BaseForm", "values": [], "variance": 0}}}}, "separator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "wtforms.fields.form.FormField.separator", "name": "separator", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.form.FormField.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.form._BoundFormT", "id": 1, "name": "_BoundFormT", "namespace": "wtforms.fields.form.FormField", "upper_bound": "wtforms.form.BaseForm", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "wtforms.fields.form.FormField"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_BoundFormT"], "typeddict_type": null}}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_BoundFormT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.form._BoundFormT", "name": "_BoundFormT", "upper_bound": "wtforms.form.BaseForm", "values": [], "variance": 0}}, "_FormErrors": {".class": "SymbolTableNode", "cross_ref": "wtforms.form._FormErrors", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_SupportsGettextAndNgettext": {".class": "SymbolTableNode", "cross_ref": "wtforms.meta._SupportsGettextAndNgettext", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_Widget": {".class": "SymbolTableNode", "cross_ref": "wtforms.fields.core._Widget", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "wtforms.fields.form.__all__", "name": "__all__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wtforms.fields.form.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wtforms.fields.form.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wtforms.fields.form.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wtforms.fields.form.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wtforms.fields.form.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wtforms.fields.form.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "C:\\Users\\<USER>\\Documents\\GitHub\\workshop\\.conda\\Lib\\site-packages\\wtforms-stubs\\fields\\form.pyi"}