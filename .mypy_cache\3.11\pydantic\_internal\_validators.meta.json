{"data_mtime": 1752485751, "dep_lines": [23, 8, 19, 20, 21, 24, 6, 8, 9, 10, 11, 12, 13, 14, 16, 18, 19, 21, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 10, 5, 5, 20, 10, 10, 5, 5, 5, 5, 5, 10, 5, 20, 5, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._import_utils", "collections.abc", "pydantic_core.core_schema", "pydantic_core._pydantic_core", "typing_inspection.typing_objects", "pydantic.errors", "__future__", "collections", "math", "re", "typing", "decimal", "fractions", "ipaddress", "zoneinfo", "typing_extensions", "pydantic_core", "typing_inspection", "builtins", "_frozen_importlib", "_typeshed", "abc", "datetime", "numbers"], "hash": "37443b75c70befb9abdfc78d4e57ae0dad61096c", "id": "pydantic._internal._validators", "ignore_all": true, "interface_hash": "3c33111bf310629904b2cf25530bdab3cedff321", "mtime": 1749927242, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Documents\\GitHub\\workshop\\.conda\\Lib\\site-packages\\pydantic\\_internal\\_validators.py", "plugin_data": null, "size": 20610, "suppressed": [], "version_id": "1.16.1"}