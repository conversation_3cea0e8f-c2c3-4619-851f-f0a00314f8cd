{"data_mtime": 1752485749, "dep_lines": [21, 21, 17, 18, 19, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 20, 10, 5, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["pydantic.v1.errors", "pydantic.v1", "re", "datetime", "typing", "builtins", "_frozen_importlib", "_typeshed", "abc", "enum", "typing_extensions"], "hash": "1a8c435b63cb82890590dd06b1f2e054e4d83bed", "id": "pydantic.v1.datetime_parse", "ignore_all": true, "interface_hash": "11d67c7ee56a6fcb96d269a4e4d405d500923c7e", "mtime": 1749927242, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Documents\\GitHub\\workshop\\.conda\\Lib\\site-packages\\pydantic\\v1\\datetime_parse.py", "plugin_data": null, "size": 7724, "suppressed": [], "version_id": "1.16.1"}