{"data_mtime": 1752485751, "dep_lines": [11, 11, 11, 12, 19, 3, 5, 6, 8, 10, 17, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 20, 5, 25, 5, 10, 5, 5, 5, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic._internal", "pydantic.main", "pydantic.fields", "__future__", "typing", "copy", "pydantic_core", "pydantic", "typing_extensions", "builtins", "_frozen_importlib", "_typeshed", "abc", "annotated_types", "pydantic._internal._generics", "pydantic.aliases", "pydantic.types", "pydantic_core._pydantic_core", "re", "types"], "hash": "fdbe700ea2fb5353c039eebe76bf7cfc7b3ddfc4", "id": "pydantic.root_model", "ignore_all": true, "interface_hash": "43f31cc6c0b26dc2862676e41af91f6343e5f398", "mtime": 1749927242, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Documents\\GitHub\\workshop\\.conda\\Lib\\site-packages\\pydantic\\root_model.py", "plugin_data": null, "size": 6279, "suppressed": [], "version_id": "1.16.1"}