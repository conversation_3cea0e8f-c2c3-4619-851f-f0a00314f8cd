{".class": "MypyFile", "_fullname": "fastapi.exception_handlers", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "HTTPException": {".class": "SymbolTableNode", "cross_ref": "starlette.exceptions.HTTPException", "kind": "Gdef"}, "HTTP_422_UNPROCESSABLE_ENTITY": {".class": "SymbolTableNode", "cross_ref": "starlette.status.HTTP_422_UNPROCESSABLE_ENTITY", "kind": "Gdef"}, "JSONResponse": {".class": "SymbolTableNode", "cross_ref": "starlette.responses.JSONResponse", "kind": "Gdef"}, "Request": {".class": "SymbolTableNode", "cross_ref": "starlette.requests.Request", "kind": "Gdef"}, "RequestValidationError": {".class": "SymbolTableNode", "cross_ref": "fastapi.exceptions.RequestValidationError", "kind": "Gdef"}, "Response": {".class": "SymbolTableNode", "cross_ref": "starlette.responses.Response", "kind": "Gdef"}, "WS_1008_POLICY_VIOLATION": {".class": "SymbolTableNode", "cross_ref": "starlette.status.WS_1008_POLICY_VIOLATION", "kind": "Gdef"}, "WebSocket": {".class": "SymbolTableNode", "cross_ref": "starlette.websockets.WebSocket", "kind": "Gdef"}, "WebSocketRequestValidationError": {".class": "SymbolTableNode", "cross_ref": "fastapi.exceptions.WebSocketRequestValidationError", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.exception_handlers.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.exception_handlers.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.exception_handlers.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.exception_handlers.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.exception_handlers.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fastapi.exception_handlers.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "http_exception_handler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["request", "exc"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "fastapi.exception_handlers.http_exception_handler", "name": "http_exception_handler", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["request", "exc"], "arg_types": ["starlette.requests.Request", "starlette.exceptions.HTTPException"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "http_exception_handler", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "starlette.responses.Response"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_body_allowed_for_status_code": {".class": "SymbolTableNode", "cross_ref": "fastapi.utils.is_body_allowed_for_status_code", "kind": "Gdef"}, "jsonable_encoder": {".class": "SymbolTableNode", "cross_ref": "fastapi.encoders.jsonable_encoder", "kind": "Gdef"}, "request_validation_exception_handler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["request", "exc"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "fastapi.exception_handlers.request_validation_exception_handler", "name": "request_validation_exception_handler", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["request", "exc"], "arg_types": ["starlette.requests.Request", "fastapi.exceptions.RequestValidationError"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "request_validation_exception_handler", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "starlette.responses.JSONResponse"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "websocket_request_validation_exception_handler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["websocket", "exc"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "fastapi.exception_handlers.websocket_request_validation_exception_handler", "name": "websocket_request_validation_exception_handler", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["websocket", "exc"], "arg_types": ["starlette.websockets.WebSocket", "fastapi.exceptions.WebSocketRequestValidationError"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "websocket_request_validation_exception_handler", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "C:\\Users\\<USER>\\Documents\\GitHub\\workshop\\.conda\\Lib\\site-packages\\fastapi\\exception_handlers.py"}