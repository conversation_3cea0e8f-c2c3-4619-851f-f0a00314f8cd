{".class": "MypyFile", "_fullname": "wtforms", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "BooleanField": {".class": "SymbolTableNode", "cross_ref": "wtforms.fields.simple.BooleanField", "kind": "Gdef"}, "ColorField": {".class": "SymbolTableNode", "cross_ref": "wtforms.fields.simple.ColorField", "kind": "Gdef"}, "DateField": {".class": "SymbolTableNode", "cross_ref": "wtforms.fields.datetime.DateField", "kind": "Gdef"}, "DateTimeField": {".class": "SymbolTableNode", "cross_ref": "wtforms.fields.datetime.DateTimeField", "kind": "Gdef"}, "DateTimeLocalField": {".class": "SymbolTableNode", "cross_ref": "wtforms.fields.datetime.DateTimeLocalField", "kind": "Gdef"}, "DecimalField": {".class": "SymbolTableNode", "cross_ref": "wtforms.fields.numeric.DecimalField", "kind": "Gdef"}, "DecimalRangeField": {".class": "SymbolTableNode", "cross_ref": "wtforms.fields.numeric.DecimalRangeField", "kind": "Gdef"}, "EmailField": {".class": "SymbolTableNode", "cross_ref": "wtforms.fields.simple.EmailField", "kind": "Gdef"}, "Field": {".class": "SymbolTableNode", "cross_ref": "wtforms.fields.core.Field", "kind": "Gdef"}, "FieldList": {".class": "SymbolTableNode", "cross_ref": "wtforms.fields.list.FieldList", "kind": "Gdef"}, "FileField": {".class": "SymbolTableNode", "cross_ref": "wtforms.fields.simple.FileField", "kind": "Gdef"}, "Final": {".class": "SymbolTableNode", "cross_ref": "typing.Final", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Flags": {".class": "SymbolTableNode", "cross_ref": "wtforms.fields.core.Flags", "kind": "Gdef"}, "FloatField": {".class": "SymbolTableNode", "cross_ref": "wtforms.fields.numeric.FloatField", "kind": "Gdef"}, "Form": {".class": "SymbolTableNode", "cross_ref": "wtforms.form.Form", "kind": "Gdef"}, "FormField": {".class": "SymbolTableNode", "cross_ref": "wtforms.fields.form.FormField", "kind": "Gdef"}, "HiddenField": {".class": "SymbolTableNode", "cross_ref": "wtforms.fields.simple.HiddenField", "kind": "Gdef"}, "IntegerField": {".class": "SymbolTableNode", "cross_ref": "wtforms.fields.numeric.IntegerField", "kind": "Gdef"}, "IntegerRangeField": {".class": "SymbolTableNode", "cross_ref": "wtforms.fields.numeric.IntegerRangeField", "kind": "Gdef"}, "Label": {".class": "SymbolTableNode", "cross_ref": "wtforms.fields.core.Label", "kind": "Gdef"}, "MonthField": {".class": "SymbolTableNode", "cross_ref": "wtforms.fields.datetime.MonthField", "kind": "Gdef"}, "MultipleFileField": {".class": "SymbolTableNode", "cross_ref": "wtforms.fields.simple.MultipleFileField", "kind": "Gdef"}, "PasswordField": {".class": "SymbolTableNode", "cross_ref": "wtforms.fields.simple.PasswordField", "kind": "Gdef"}, "RadioField": {".class": "SymbolTableNode", "cross_ref": "wtforms.fields.choices.RadioField", "kind": "Gdef"}, "SearchField": {".class": "SymbolTableNode", "cross_ref": "wtforms.fields.simple.SearchField", "kind": "Gdef"}, "SelectField": {".class": "SymbolTableNode", "cross_ref": "wtforms.fields.choices.SelectField", "kind": "Gdef"}, "SelectFieldBase": {".class": "SymbolTableNode", "cross_ref": "wtforms.fields.choices.SelectFieldBase", "kind": "Gdef"}, "SelectMultipleField": {".class": "SymbolTableNode", "cross_ref": "wtforms.fields.choices.SelectMultipleField", "kind": "Gdef"}, "StringField": {".class": "SymbolTableNode", "cross_ref": "wtforms.fields.simple.StringField", "kind": "Gdef"}, "SubmitField": {".class": "SymbolTableNode", "cross_ref": "wtforms.fields.simple.SubmitField", "kind": "Gdef"}, "TelField": {".class": "SymbolTableNode", "cross_ref": "wtforms.fields.simple.TelField", "kind": "Gdef"}, "TextAreaField": {".class": "SymbolTableNode", "cross_ref": "wtforms.fields.simple.TextAreaField", "kind": "Gdef"}, "TimeField": {".class": "SymbolTableNode", "cross_ref": "wtforms.fields.datetime.TimeField", "kind": "Gdef"}, "URLField": {".class": "SymbolTableNode", "cross_ref": "wtforms.fields.simple.URLField", "kind": "Gdef"}, "ValidationError": {".class": "SymbolTableNode", "cross_ref": "wtforms.validators.ValidationError", "kind": "Gdef"}, "WeekField": {".class": "SymbolTableNode", "cross_ref": "wtforms.fields.datetime.WeekField", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "wtforms.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wtforms.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wtforms.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wtforms.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wtforms.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wtforms.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wtforms.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wtforms.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "__version__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "wtforms.__version__", "name": "__version__", "setter_type": null, "type": "builtins.str"}}, "validators": {".class": "SymbolTableNode", "cross_ref": "wtforms.validators", "kind": "Gdef"}, "widgets": {".class": "SymbolTableNode", "cross_ref": "wtforms.widgets", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\Documents\\GitHub\\workshop\\.conda\\Lib\\site-packages\\wtforms-stubs\\__init__.pyi"}