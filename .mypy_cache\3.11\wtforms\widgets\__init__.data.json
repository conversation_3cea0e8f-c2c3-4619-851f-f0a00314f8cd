{".class": "MypyFile", "_fullname": "wtforms.widgets", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "CheckboxInput": {".class": "SymbolTableNode", "cross_ref": "wtforms.widgets.core.CheckboxInput", "kind": "Gdef"}, "ColorInput": {".class": "SymbolTableNode", "cross_ref": "wtforms.widgets.core.ColorInput", "kind": "Gdef"}, "DateInput": {".class": "SymbolTableNode", "cross_ref": "wtforms.widgets.core.DateInput", "kind": "Gdef"}, "DateTimeInput": {".class": "SymbolTableNode", "cross_ref": "wtforms.widgets.core.DateTimeInput", "kind": "Gdef"}, "DateTimeLocalInput": {".class": "SymbolTableNode", "cross_ref": "wtforms.widgets.core.DateTimeLocalInput", "kind": "Gdef"}, "EmailInput": {".class": "SymbolTableNode", "cross_ref": "wtforms.widgets.core.EmailInput", "kind": "Gdef"}, "FileInput": {".class": "SymbolTableNode", "cross_ref": "wtforms.widgets.core.FileInput", "kind": "Gdef"}, "HiddenInput": {".class": "SymbolTableNode", "cross_ref": "wtforms.widgets.core.HiddenInput", "kind": "Gdef"}, "Input": {".class": "SymbolTableNode", "cross_ref": "wtforms.widgets.core.Input", "kind": "Gdef"}, "ListWidget": {".class": "SymbolTableNode", "cross_ref": "wtforms.widgets.core.ListWidget", "kind": "Gdef"}, "MonthInput": {".class": "SymbolTableNode", "cross_ref": "wtforms.widgets.core.MonthInput", "kind": "Gdef"}, "NumberInput": {".class": "SymbolTableNode", "cross_ref": "wtforms.widgets.core.NumberInput", "kind": "Gdef"}, "Option": {".class": "SymbolTableNode", "cross_ref": "wtforms.widgets.core.Option", "kind": "Gdef"}, "PasswordInput": {".class": "SymbolTableNode", "cross_ref": "wtforms.widgets.core.PasswordInput", "kind": "Gdef"}, "RadioInput": {".class": "SymbolTableNode", "cross_ref": "wtforms.widgets.core.RadioInput", "kind": "Gdef"}, "RangeInput": {".class": "SymbolTableNode", "cross_ref": "wtforms.widgets.core.RangeInput", "kind": "Gdef"}, "SearchInput": {".class": "SymbolTableNode", "cross_ref": "wtforms.widgets.core.SearchInput", "kind": "Gdef"}, "Select": {".class": "SymbolTableNode", "cross_ref": "wtforms.widgets.core.Select", "kind": "Gdef"}, "SubmitInput": {".class": "SymbolTableNode", "cross_ref": "wtforms.widgets.core.SubmitInput", "kind": "Gdef"}, "TableWidget": {".class": "SymbolTableNode", "cross_ref": "wtforms.widgets.core.TableWidget", "kind": "Gdef"}, "TelInput": {".class": "SymbolTableNode", "cross_ref": "wtforms.widgets.core.TelInput", "kind": "Gdef"}, "TextArea": {".class": "SymbolTableNode", "cross_ref": "wtforms.widgets.core.TextArea", "kind": "Gdef"}, "TextInput": {".class": "SymbolTableNode", "cross_ref": "wtforms.widgets.core.TextInput", "kind": "Gdef"}, "TimeInput": {".class": "SymbolTableNode", "cross_ref": "wtforms.widgets.core.TimeInput", "kind": "Gdef"}, "URLInput": {".class": "SymbolTableNode", "cross_ref": "wtforms.widgets.core.URLInput", "kind": "Gdef"}, "WeekInput": {".class": "SymbolTableNode", "cross_ref": "wtforms.widgets.core.WeekInput", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "wtforms.widgets.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wtforms.widgets.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wtforms.widgets.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wtforms.widgets.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wtforms.widgets.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wtforms.widgets.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wtforms.widgets.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wtforms.widgets.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "html_params": {".class": "SymbolTableNode", "cross_ref": "wtforms.widgets.core.html_params", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\Documents\\GitHub\\workshop\\.conda\\Lib\\site-packages\\wtforms-stubs\\widgets\\__init__.pyi"}