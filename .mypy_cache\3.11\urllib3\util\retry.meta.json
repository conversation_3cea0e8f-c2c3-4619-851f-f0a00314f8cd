{"data_mtime": 1752486190, "dep_lines": [21, 12, 26, 27, 1, 3, 4, 5, 6, 7, 8, 9, 10, 24, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 25, 25, 5, 10, 10, 10, 10, 10, 10, 5, 5, 25, 5, 30, 30, 30, 30], "dependencies": ["urllib3.util.util", "urllib3.exceptions", "urllib3.connectionpool", "urllib3.response", "__future__", "email", "logging", "random", "re", "time", "typing", "itertools", "types", "typing_extensions", "builtins", "_frozen_importlib", "_io", "abc", "io"], "hash": "c718dec62fedd1780fbde1b9f6c96b4460a5383f", "id": "urllib3.util.retry", "ignore_all": true, "interface_hash": "87bbf08d03a7ba3f6f842086e833b8a758a6c7d2", "mtime": 1750271475, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Documents\\GitHub\\workshop\\.conda\\Lib\\site-packages\\urllib3\\util\\retry.py", "plugin_data": null, "size": 18459, "suppressed": [], "version_id": "1.16.1"}