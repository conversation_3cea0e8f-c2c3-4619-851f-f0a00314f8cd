{".class": "MypyFile", "_fullname": "urllib3.util.ssl_match_hostname", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CertificateError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.ValueError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.util.ssl_match_hostname.CertificateError", "name": "CertificateError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "urllib3.util.ssl_match_hostname.CertificateError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.util.ssl_match_hostname", "mro": ["urllib3.util.ssl_match_hostname.CertificateError", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.util.ssl_match_hostname.CertificateError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.util.ssl_match_hostname.CertificateError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IPv4Address": {".class": "SymbolTableNode", "cross_ref": "ipaddress.IPv4Address", "kind": "Gdef"}, "IPv6Address": {".class": "SymbolTableNode", "cross_ref": "ipaddress.IPv6Address", "kind": "Gdef"}, "_TYPE_PEER_CERT_RET_DICT": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.ssl_._TYPE_PEER_CERT_RET_DICT", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.util.ssl_match_hostname.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.util.ssl_match_hostname.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.util.ssl_match_hostname.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.util.ssl_match_hostname.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.util.ssl_match_hostname.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.util.ssl_match_hostname.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "__version__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "urllib3.util.ssl_match_hostname.__version__", "name": "__version__", "setter_type": null, "type": "builtins.str"}}, "_dnsname_match": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["dn", "hostname", "max_wildcards"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.util.ssl_match_hostname._dnsname_match", "name": "_dnsname_match", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["dn", "hostname", "max_wildcards"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", "builtins.int"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_dnsname_match", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}, "builtins.bool"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_ipaddress_match": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["ipname", "host_ip"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.util.ssl_match_hostname._ipaddress_match", "name": "_ipaddress_match", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["ipname", "host_ip"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["ipaddress.IPv4Address", "ipaddress.IPv6Address"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_ipaddress_match", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "ipaddress": {".class": "SymbolTableNode", "cross_ref": "ipaddress", "kind": "Gdef"}, "match_hostname": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["cert", "hostname", "hostname_checks_common_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.util.ssl_match_hostname.match_hostname", "name": "match_hostname", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cert", "hostname", "hostname_checks_common_name"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.ssl_._TYPE_PEER_CERT_RET_DICT"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "match_hostname", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\Documents\\GitHub\\workshop\\.conda\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py"}