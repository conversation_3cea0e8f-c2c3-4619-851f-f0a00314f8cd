{"data_mtime": 1752486191, "dep_lines": [1, 1, 1, 1, 1], "dep_prios": [5, 5, 30, 30, 30], "dependencies": ["wtforms.widgets.core", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "f96c108a03c39a8a4a3f4f40eec1e2834ba9354b", "id": "wtforms.widgets", "ignore_all": true, "interface_hash": "abba49d52b9128540035cd5048c68d52119ea40c", "mtime": 1752485783, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Documents\\GitHub\\workshop\\.conda\\Lib\\site-packages\\wtforms-stubs\\widgets\\__init__.pyi", "plugin_data": null, "size": 1345, "suppressed": [], "version_id": "1.16.1"}