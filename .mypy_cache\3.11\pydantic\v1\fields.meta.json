{"data_mtime": 1752485749, "dep_lines": [31, 32, 33, 35, 36, 51, 62, 87, 493, 1163, 4, 31, 1, 2, 3, 5, 29, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 25, 20, 20, 5, 20, 10, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic.v1.errors", "pydantic.v1.class_validators", "pydantic.v1.error_wrappers", "pydantic.v1.types", "pydantic.v1.typing", "pydantic.v1.utils", "pydantic.v1.validators", "pydantic.v1.config", "pydantic.v1.schema", "pydantic.v1.main", "collections.abc", "pydantic.v1", "copy", "re", "collections", "typing", "typing_extensions", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "pydantic.v1.dataclasses", "types"], "hash": "a21d6b4f6d5c84c2211be2afcb9d94c2f315d425", "id": "pydantic.v1.fields", "ignore_all": true, "interface_hash": "2716adf31ef586bf4d28276d13783756097feb03", "mtime": 1749927242, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Documents\\GitHub\\workshop\\.conda\\Lib\\site-packages\\pydantic\\v1\\fields.py", "plugin_data": null, "size": 50649, "suppressed": [], "version_id": "1.16.1"}