{"data_mtime": 1752486191, "dep_lines": [9, 10, 11, 12, 13, 14, 15, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["subprocess", "time", "sys", "os", "signal", "requests", "pathlib", "builtins", "_frozen_importlib", "_sitebuiltins", "_typeshed", "abc", "enum", "http", "http.cookiejar", "requests.api", "requests.auth", "requests.models", "types", "typing"], "hash": "0b9b2f23c246da8ade707afd8cb49e4b0efbf785", "id": "start_integrated_system", "ignore_all": false, "interface_hash": "bb379ed562c504f8898383fef84c548902e34175", "mtime": 1752485379, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": ".\\start_integrated_system.py", "plugin_data": null, "size": 8024, "suppressed": [], "version_id": "1.16.1"}