Metadata-Version: 2.4
Name: types-WTForms
Version: 3.2.1.20250602
Summary: Typing stubs for WTForms
License-Expression: Apache-2.0
Project-URL: Homepage, https://github.com/python/typeshed
Project-URL: GitHub, https://github.com/python/typeshed
Project-URL: Changes, https://github.com/typeshed-internal/stub_uploader/blob/main/data/changelogs/WTForms.md
Project-URL: Issue tracker, https://github.com/python/typeshed/issues
Project-URL: Chat, https://gitter.im/python/typing
Classifier: Programming Language :: Python :: 3
Classifier: Typing :: Stubs Only
Requires-Python: >=3.9
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: MarkupSafe
Dynamic: license-file

## Typing stubs for WTForms

This is a [PEP 561](https://peps.python.org/pep-0561/)
type stub package for the [`WTForms`](https://github.com/pallets-eco/wtforms) package.
It can be used by type-checking tools like
[mypy](https://github.com/python/mypy/),
[pyright](https://github.com/microsoft/pyright),
[pytype](https://github.com/google/pytype/),
[Pyre](https://pyre-check.org/),
PyCharm, etc. to check code that uses `WTForms`. This version of
`types-WTForms` aims to provide accurate annotations for
`WTForms~=3.2.1`.

This package is part of the [typeshed project](https://github.com/python/typeshed).
All fixes for types and metadata should be contributed there.
See [the README](https://github.com/python/typeshed/blob/main/README.md)
for more details. The source for this package can be found in the
[`stubs/WTForms`](https://github.com/python/typeshed/tree/main/stubs/WTForms)
directory.

This package was tested with
mypy 1.16.0,
pyright 1.1.400,
and pytype 2024.10.11.
It was generated from typeshed commit
[`eb495ff1353a538a5021341b3c905c35a2284899`](https://github.com/python/typeshed/commit/eb495ff1353a538a5021341b3c905c35a2284899).
