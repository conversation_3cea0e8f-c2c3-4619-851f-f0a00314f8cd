{".class": "MypyFile", "_fullname": "wtforms.fields.choices", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BaseForm": {".class": "SymbolTableNode", "cross_ref": "wtforms.form.BaseForm", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DefaultMeta": {".class": "SymbolTableNode", "cross_ref": "wtforms.meta.DefaultMeta", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Field": {".class": "SymbolTableNode", "cross_ref": "wtforms.fields.core.Field", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RadioField": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["wtforms.fields.choices.SelectField"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "wtforms.fields.choices.RadioField", "name": "RadioField", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "wtforms.fields.choices.RadioField", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "wtforms.fields.choices", "mro": ["wtforms.fields.choices.RadioField", "wtforms.fields.choices.SelectField", "wtforms.fields.choices.SelectFieldBase", "wtforms.fields.core.Field", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.choices.RadioField.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.fields.choices.RadioField", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SelectField": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["wtforms.fields.choices.SelectFieldBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "wtforms.fields.choices.SelectField", "name": "SelectField", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "wtforms.fields.choices.SelectField", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "wtforms.fields.choices", "mro": ["wtforms.fields.choices.SelectField", "wtforms.fields.choices.SelectFieldBase", "wtforms.fields.core.Field", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "label", "validators", "coerce", "choices", "validate_choice", "filters", "description", "id", "default", "widget", "option_widget", "render_kw", "name", "_form", "_prefix", "_translations", "_meta"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "wtforms.fields.choices.SelectField.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "label", "validators", "coerce", "choices", "validate_choice", "filters", "description", "id", "default", "widget", "option_widget", "render_kw", "name", "_form", "_prefix", "_translations", "_meta"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.choices.SelectField.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.fields.choices.SelectField", "values": [], "variance": 0}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.core._FormT", "id": -1, "name": "_FormT", "namespace": "wtforms.fields.choices.SelectField.__init__", "upper_bound": "wtforms.form.BaseForm", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.choices.SelectField.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.fields.choices.SelectField", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "wtforms.fields.core._Validator"}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "wtforms.fields.choices._Choice"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "TypeAliasType", "args": [], "type_ref": "wtforms.fields.choices._GroupedChoices"}, {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "wtforms.fields.choices._Choice"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "TypeAliasType", "args": [], "type_ref": "wtforms.fields.choices._GroupedChoices"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "wtforms.fields.core._Filter"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.choices.SelectField.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.fields.choices.SelectField", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "wtforms.fields.core._Widget"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["wtforms.fields.choices.SelectFieldBase._Option"], "extra_attrs": null, "type_ref": "wtforms.fields.core._Widget"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["wtforms.form.BaseForm", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": ["wtforms.meta._SupportsGettextAndNgettext", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["wtforms.meta.DefaultMeta", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of SelectField", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.choices.SelectField.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.fields.choices.SelectField", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.core._FormT", "id": -1, "name": "_FormT", "namespace": "wtforms.fields.choices.SelectField.__init__", "upper_bound": "wtforms.form.BaseForm", "values": [], "variance": 0}]}}}, "choices": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "wtforms.fields.choices.SelectField.choices", "name": "choices", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "wtforms.fields.choices._Choice"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypeAliasType", "args": [], "type_ref": "wtforms.fields.choices._GroupedChoices"}], "uses_pep604_syntax": true}}}, "coerce": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "wtforms.fields.choices.SelectField.coerce", "name": "coerce", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "has_groups": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "wtforms.fields.choices.SelectField.has_groups", "name": "has_groups", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["wtforms.fields.choices.SelectField"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "has_groups of SelectField", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "iter_choices": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "wtforms.fields.choices.SelectField.iter_choices", "name": "iter_choices", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["wtforms.fields.choices.SelectField"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "iter_choices of SelectField", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "wtforms.fields.choices._FullChoice"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "iter_groups": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "wtforms.fields.choices.SelectField.iter_groups", "name": "iter_groups", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["wtforms.fields.choices.SelectField"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "iter_groups of SelectField", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "wtforms.fields.choices._FullGroupedChoices"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "validate_choice": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "wtforms.fields.choices.SelectField.validate_choice", "name": "validate_choice", "setter_type": null, "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.choices.SelectField.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.fields.choices.SelectField", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SelectFieldBase": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["wtforms.fields.core.Field"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "wtforms.fields.choices.SelectFieldBase", "name": "SelectFieldBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "wtforms.fields.choices.SelectFieldBase", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "wtforms.fields.choices", "mro": ["wtforms.fields.choices.SelectFieldBase", "wtforms.fields.core.Field", "builtins.object"], "names": {".class": "SymbolTable", "_Option": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["wtforms.fields.core.Field"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "wtforms.fields.choices.SelectFieldBase._Option", "name": "_Option", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "wtforms.fields.choices.SelectFieldBase._Option", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "wtforms.fields.choices", "mro": ["wtforms.fields.choices.SelectFieldBase._Option", "wtforms.fields.core.Field", "builtins.object"], "names": {".class": "SymbolTable", "checked": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "wtforms.fields.choices.SelectFieldBase._Option.checked", "name": "checked", "setter_type": null, "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.choices.SelectFieldBase._Option.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.fields.choices.SelectFieldBase._Option", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "label", "validators", "option_widget", "filters", "description", "id", "default", "widget", "render_kw", "name", "_form", "_prefix", "_translations", "_meta"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "wtforms.fields.choices.SelectFieldBase.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "label", "validators", "option_widget", "filters", "description", "id", "default", "widget", "render_kw", "name", "_form", "_prefix", "_translations", "_meta"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.choices.SelectFieldBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.fields.choices.SelectFieldBase", "values": [], "variance": 0}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.core._FormT", "id": -1, "name": "_FormT", "namespace": "wtforms.fields.choices.SelectFieldBase.__init__", "upper_bound": "wtforms.form.BaseForm", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.choices.SelectFieldBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.fields.choices.SelectFieldBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "wtforms.fields.core._Validator"}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["wtforms.fields.choices.SelectFieldBase._Option"], "extra_attrs": null, "type_ref": "wtforms.fields.core._Widget"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "wtforms.fields.core._Filter"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.choices.SelectFieldBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.fields.choices.SelectFieldBase", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "wtforms.fields.core._Widget"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["wtforms.form.BaseForm", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": ["wtforms.meta._SupportsGettextAndNgettext", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["wtforms.meta.DefaultMeta", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of SelectFieldBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.choices.SelectFieldBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.fields.choices.SelectFieldBase", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.core._FormT", "id": -1, "name": "_FormT", "namespace": "wtforms.fields.choices.SelectFieldBase.__init__", "upper_bound": "wtforms.form.BaseForm", "values": [], "variance": 0}]}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "wtforms.fields.choices.SelectFieldBase.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["wtforms.fields.choices.SelectFieldBase"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__iter__ of SelectFieldBase", "ret_type": {".class": "Instance", "args": ["wtforms.fields.choices.SelectFieldBase._Option"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "has_groups": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "wtforms.fields.choices.SelectFieldBase.has_groups", "name": "has_groups", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["wtforms.fields.choices.SelectFieldBase"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "has_groups of SelectFieldBase", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "iter_choices": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "wtforms.fields.choices.SelectFieldBase.iter_choices", "name": "iter_choices", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["wtforms.fields.choices.SelectFieldBase"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "iter_choices of SelectFieldBase", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "wtforms.fields.choices._FullChoice"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "iter_groups": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "wtforms.fields.choices.SelectFieldBase.iter_groups", "name": "iter_groups", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["wtforms.fields.choices.SelectFieldBase"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "iter_groups of SelectFieldBase", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "wtforms.fields.choices._FullGroupedChoices"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "option_widget": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "wtforms.fields.choices.SelectFieldBase.option_widget", "name": "option_widget", "setter_type": null, "type": {".class": "Instance", "args": ["wtforms.fields.choices.SelectFieldBase._Option"], "extra_attrs": null, "type_ref": "wtforms.fields.core._Widget"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.choices.SelectFieldBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.fields.choices.SelectFieldBase", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SelectMultipleField": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["wtforms.fields.choices.SelectField"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "wtforms.fields.choices.SelectMultipleField", "name": "SelectMultipleField", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "wtforms.fields.choices.SelectMultipleField", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "wtforms.fields.choices", "mro": ["wtforms.fields.choices.SelectMultipleField", "wtforms.fields.choices.SelectField", "wtforms.fields.choices.SelectFieldBase", "wtforms.fields.core.Field", "builtins.object"], "names": {".class": "SymbolTable", "data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "wtforms.fields.choices.SelectMultipleField.data", "name": "data", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.choices.SelectMultipleField.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.fields.choices.SelectMultipleField", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing.Self", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_Choice": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "wtforms.fields.choices._Choice", "line": 12, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}}}, "_Filter": {".class": "SymbolTableNode", "cross_ref": "wtforms.fields.core._Filter", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FormT": {".class": "SymbolTableNode", "cross_ref": "wtforms.fields.core._FormT", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FullChoice": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "wtforms.fields.choices._FullChoice", "line": 15, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.str", "builtins.bool", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_FullGroupedChoices": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "wtforms.fields.choices._FullGroupedChoices", "line": 16, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "wtforms.fields.choices._FullChoice"}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_GroupedChoices": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "wtforms.fields.choices._GroupedChoices", "line": 14, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_Option": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "wtforms.fields.choices._Option", "line": 17, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "wtforms.fields.choices.SelectFieldBase._Option"}}, "_SupportsGettextAndNgettext": {".class": "SymbolTableNode", "cross_ref": "wtforms.meta._SupportsGettextAndNgettext", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_Validator": {".class": "SymbolTableNode", "cross_ref": "wtforms.fields.core._Validator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_Widget": {".class": "SymbolTableNode", "cross_ref": "wtforms.fields.core._Widget", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "wtforms.fields.choices.__all__", "name": "__all__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wtforms.fields.choices.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wtforms.fields.choices.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wtforms.fields.choices.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wtforms.fields.choices.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wtforms.fields.choices.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wtforms.fields.choices.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "C:\\Users\\<USER>\\Documents\\GitHub\\workshop\\.conda\\Lib\\site-packages\\wtforms-stubs\\fields\\choices.pyi"}