{"data_mtime": 1752485753, "dep_lines": [2, 7, 3, 4, 1, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.ext.declarative", "app.core.config", "sqlalchemy.orm", "sqlalchemy.pool", "sqlalchemy", "logging", "builtins", "_frozen_importlib", "abc", "enum", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "pydantic_settings", "pydantic_settings.main", "sqlalchemy.engine", "sqlalchemy.engine.base", "sqlalchemy.engine.create", "sqlalchemy.engine.interfaces", "sqlalchemy.engine.url", "sqlalchemy.event", "sqlalchemy.event.base", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.log", "sqlalchemy.orm.session", "sqlalchemy.pool.base", "sqlalchemy.pool.impl", "sqlalchemy.util", "sqlalchemy.util._py_collections", "typing"], "hash": "950098a547676a359b9ad87fa1f978027e3148bc", "id": "app.core.database", "ignore_all": false, "interface_hash": "2e35e6eb08cc4405aec9b7ae857642c7d48951ec", "mtime": 1752483544, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": ".\\app\\core\\database.py", "plugin_data": null, "size": 1142, "suppressed": [], "version_id": "1.16.1"}