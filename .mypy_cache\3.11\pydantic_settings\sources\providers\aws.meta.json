{"data_mtime": **********, "dep_lines": [8, 7, 4, 11, 1, 3, 5, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 25, 5, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic_settings.sources.providers.env", "pydantic_settings.sources.utils", "collections.abc", "pydantic_settings.main", "__future__", "json", "typing", "builtins", "_frozen_importlib", "abc", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "pydantic_settings.sources.base"], "hash": "d61faaaa865cca1e8cfd10163d24ec36241717ba", "id": "pydantic_settings.sources.providers.aws", "ignore_all": true, "interface_hash": "c344c0e4368a2eb2b30e29726125f7360e23916a", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Documents\\GitHub\\workshop\\.conda\\Lib\\site-packages\\pydantic_settings\\sources\\providers\\aws.py", "plugin_data": null, "size": 2416, "suppressed": [], "version_id": "1.16.1"}