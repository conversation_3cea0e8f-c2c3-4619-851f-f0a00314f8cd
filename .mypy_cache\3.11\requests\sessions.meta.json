{"data_mtime": 1752486191, "dep_lines": [2, 6, 6, 6, 6, 6, 6, 6, 6, 6, 8, 1, 3, 4, 6, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 10, 10, 10, 10, 5, 10, 10, 5, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["collections.abc", "requests.adapters", "requests.auth", "requests.compat", "requests.cookies", "requests.exceptions", "requests.hooks", "requests.models", "requests.status_codes", "requests.utils", "requests.structures", "_typeshed", "typing", "typing_extensions", "requests", "builtins", "_frozen_importlib", "abc", "collections", "http", "http.cookiejar", "types", "urllib3", "urllib3.exceptions", "urllib3.util", "urllib3.util.retry"], "hash": "ee4617d7b45c25d2d57aa331ea7a61b2a756cc22", "id": "requests.sessions", "ignore_all": true, "interface_hash": "7b89e7e64c0a7fe5452458990495e087fa974e82", "mtime": 1752485783, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Documents\\GitHub\\workshop\\.conda\\Lib\\site-packages\\requests-stubs\\sessions.pyi", "plugin_data": null, "size": 11672, "suppressed": [], "version_id": "1.16.1"}