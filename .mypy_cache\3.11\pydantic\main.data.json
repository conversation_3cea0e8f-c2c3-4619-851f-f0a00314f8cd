{".class": "MypyFile", "_fullname": "pydantic.main", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AbstractSetIntStr": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._utils.AbstractSetIntStr", "kind": "Gdef", "module_public": false}, "AliasChoices": {".class": "SymbolTableNode", "cross_ref": "pydantic.aliases.AliasChoices", "kind": "Gdef", "module_public": false}, "AliasPath": {".class": "SymbolTableNode", "cross_ref": "pydantic.aliases.AliasPath", "kind": "Gdef", "module_public": false}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "BaseModel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": "pydantic._internal._model_construction.ModelMetaclass", "defn": {".class": "ClassDef", "fullname": "pydantic.main.BaseModel", "name": "BaseModel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic.main.BaseModel", "has_param_spec_type": false, "metaclass_type": "pydantic._internal._model_construction.ModelMetaclass", "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 214, "name": "__pydantic_extra__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 217, "name": "__pydantic_fields_set__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 220, "name": "__pydantic_private__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "pydantic.main", "mro": ["pydantic.main.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__class_getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "typevar_values"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_trivial_self"], "fullname": "pydantic.main.BaseModel.__class_getitem__", "name": "__class_getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "typevar_values"], "arg_types": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__class_getitem__ of BaseModel", "ret_type": {".class": "UnionType", "items": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}, "pydantic._internal._forward_ref.PydanticRecursiveRef"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__class_vars__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "pydantic.main.BaseModel.__class_vars__", "name": "__class_vars__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "__copy__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.main.BaseModel.__copy__", "name": "__copy__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__copy__ of BaseModel", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}}, "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "pydantic.main.BaseModel.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__deepcopy__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "memo"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.main.BaseModel.__deepcopy__", "name": "__deepcopy__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "memo"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__deepcopy__ of BaseModel", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}}, "__fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": "function pydantic.main.BaseModel.__fields__ is deprecated: The `__fields__` attribute is deprecated, use `model_fields` instead.", "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "pydantic.main.BaseModel.__fields__", "name": "__fields__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.main.BaseModel"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__fields__ of BaseModel", "ret_type": {".class": "Instance", "args": ["builtins.str", "pydantic.fields.FieldInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "pydantic.main.BaseModel.__fields__", "name": "__fields__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.main.BaseModel"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__fields__ of BaseModel", "ret_type": {".class": "Instance", "args": ["builtins.str", "pydantic.fields.FieldInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__fields_set__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": "function pydantic.main.BaseModel.__fields_set__ is deprecated: The `__fields_set__` attribute is deprecated, use `model_fields_set` instead.", "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "pydantic.main.BaseModel.__fields_set__", "name": "__fields_set__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.main.BaseModel"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__fields_set__ of BaseModel", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "pydantic.main.BaseModel.__fields_set__", "name": "__fields_set__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.main.BaseModel"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__fields_set__ of BaseModel", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__get_pydantic_core_schema__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "pydantic.main.BaseModel.__get_pydantic_core_schema__", "name": "__get_pydantic_core_schema__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}, {".class": "TypeType", "item": "pydantic.main.BaseModel"}, "pydantic.annotated_handlers.GetCoreSchemaHandler"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__get_pydantic_core_schema__ of BaseModel", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic.main.BaseModel.__get_pydantic_core_schema__", "name": "__get_pydantic_core_schema__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}, {".class": "TypeType", "item": "pydantic.main.BaseModel"}, "pydantic.annotated_handlers.GetCoreSchemaHandler"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__get_pydantic_core_schema__ of BaseModel", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__get_pydantic_json_schema__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "pydantic.main.BaseModel.__get_pydantic_json_schema__", "name": "__get_pydantic_json_schema__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "pydantic.annotated_handlers.GetJsonSchemaHandler"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__get_pydantic_json_schema__ of BaseModel", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic.main.BaseModel.__get_pydantic_json_schema__", "name": "__get_pydantic_json_schema__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "pydantic.annotated_handlers.GetJsonSchemaHandler"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__get_pydantic_json_schema__ of BaseModel", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__getstate__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.main.BaseModel.__getstate__", "name": "__getstate__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.main.BaseModel"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__getstate__ of BaseModel", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": [null, "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.main.BaseModel.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": [null, "data"], "arg_types": ["pydantic.main.BaseModel", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of BaseModel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init_subclass__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["cls", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_trivial_self", "is_mypy_only"], "fullname": "pydantic.main.BaseModel.__init_subclass__", "name": "__init_subclass__", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["cls", "kwargs"], "arg_types": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}, {".class": "TypedDictType", "fallback": "pydantic.config.ConfigDict", "items": [["title", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["model_title_generator", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.type"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["field_title_generator", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["pydantic.fields.FieldInfo", "pydantic.fields.ComputedFieldInfo"], "uses_pep604_syntax": true}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["str_to_lower", "builtins.bool"], ["str_to_upper", "builtins.bool"], ["str_strip_whitespace", "builtins.bool"], ["str_min_length", "builtins.int"], ["str_max_length", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["extra", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.config.ExtraValues"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["frozen", "builtins.bool"], ["populate_by_name", "builtins.bool"], ["use_enum_values", "builtins.bool"], ["validate_assignment", "builtins.bool"], ["arbitrary_types_allowed", "builtins.bool"], ["from_attributes", "builtins.bool"], ["loc_by_alias", "builtins.bool"], ["alias_generator", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "pydantic.aliases.AliasGenerator", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["ignored_types", {".class": "Instance", "args": ["builtins.type"], "extra_attrs": null, "type_ref": "builtins.tuple"}], ["allow_inf_nan", "builtins.bool"], ["json_schema_extra", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.config.JsonDict"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.config.JsonSchemaExtraCallable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["json_encoders", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeType", "item": "builtins.object"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.config.JsonEncoder"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["strict", "builtins.bool"], ["revalidate_instances", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "always"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "never"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "subclass-instances"}], "uses_pep604_syntax": false}], ["ser_j<PERSON>_<PERSON><PERSON><PERSON>", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "iso8601"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "float"}], "uses_pep604_syntax": false}], ["ser_json_bytes", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "utf8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "base64"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hex"}], "uses_pep604_syntax": false}], ["val_json_bytes", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "utf8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "base64"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hex"}], "uses_pep604_syntax": false}], ["ser_json_inf_nan", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "null"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "constants"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "strings"}], "uses_pep604_syntax": false}], ["validate_default", "builtins.bool"], ["validate_return", "builtins.bool"], ["protected_namespaces", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.tuple"}], ["hide_input_in_errors", "builtins.bool"], ["defer_build", "builtins.bool"], ["plugin_settings", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["schema_generator", {".class": "UnionType", "items": [{".class": "TypeType", "item": "pydantic._internal._generate_schema.GenerateSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["json_schema_serialization_defaults_required", "builtins.bool"], ["json_schema_mode_override", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "validation"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "serialization"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], ["coerce_numbers_to_str", "builtins.bool"], ["regex_engine", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "rust-regex"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "python-re"}], "uses_pep604_syntax": false}], ["validation_error_cause", "builtins.bool"], ["use_attribute_docstrings", "builtins.bool"], ["cache_strings", {".class": "UnionType", "items": ["builtins.bool", {".class": "LiteralType", "fallback": "builtins.str", "value": "all"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "keys"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "none"}], "uses_pep604_syntax": true}], ["validate_by_alias", "builtins.bool"], ["validate_by_name", "builtins.bool"], ["serialize_by_alias", "builtins.bool"]], "readonly_keys": [], "required_keys": []}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init_subclass__ of BaseModel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": true, "variables": []}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.main.BaseModel.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["pydantic.main.BaseModel"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__iter__ of BaseModel", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.main.TupleGenerator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "pydantic.main.BaseModel.__match_args__", "name": "__match_args__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "pydantic.main.BaseModel.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of BaseModel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "pydantic.main.BaseModel.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of BaseModel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "__pretty__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pydantic.main.BaseModel.__pretty__", "name": "__pretty__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "fmt", "kwargs"], "arg_types": ["pydantic._internal._repr.Representation", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__private_attributes__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "pydantic.main.BaseModel.__private_attributes__", "name": "__private_attributes__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "pydantic.fields.ModelPrivateAttr"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__pydantic_complete__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "pydantic.main.BaseModel.__pydantic_complete__", "name": "__pydantic_complete__", "setter_type": null, "type": "builtins.bool"}}, "__pydantic_computed_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "pydantic.main.BaseModel.__pydantic_computed_fields__", "name": "__pydantic_computed_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "pydantic.fields.ComputedFieldInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__pydantic_core_schema__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "pydantic.main.BaseModel.__pydantic_core_schema__", "name": "__pydantic_core_schema__", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}}}, "__pydantic_custom_init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "pydantic.main.BaseModel.__pydantic_custom_init__", "name": "__pydantic_custom_init__", "setter_type": null, "type": "builtins.bool"}}, "__pydantic_decorators__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "pydantic.main.BaseModel.__pydantic_decorators__", "name": "__pydantic_decorators__", "setter_type": null, "type": "pydantic._internal._decorators.DecoratorInfos"}}, "__pydantic_extra__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "pydantic.main.BaseModel.__pydantic_extra__", "name": "__pydantic_extra__", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "__pydantic_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "pydantic.main.BaseModel.__pydantic_fields__", "name": "__pydantic_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "pydantic.fields.FieldInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__pydantic_fields_set__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "pydantic.main.BaseModel.__pydantic_fields_set__", "name": "__pydantic_fields_set__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "__pydantic_generic_metadata__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "pydantic.main.BaseModel.__pydantic_generic_metadata__", "name": "__pydantic_generic_metadata__", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._generics.PydanticGenericMetadata"}}}, "__pydantic_init_subclass__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["cls", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "pydantic.main.BaseModel.__pydantic_init_subclass__", "name": "__pydantic_init_subclass__", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["cls", "kwargs"], "arg_types": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__pydantic_init_subclass__ of BaseModel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic.main.BaseModel.__pydantic_init_subclass__", "name": "__pydantic_init_subclass__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["cls", "kwargs"], "arg_types": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__pydantic_init_subclass__ of BaseModel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__pydantic_parent_namespace__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "pydantic.main.BaseModel.__pydantic_parent_namespace__", "name": "__pydantic_parent_namespace__", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "__pydantic_post_init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "pydantic.main.BaseModel.__pydantic_post_init__", "name": "__pydantic_post_init__", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "model_post_init"}], "uses_pep604_syntax": true}}}, "__pydantic_private__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "pydantic.main.BaseModel.__pydantic_private__", "name": "__pydantic_private__", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "__pydantic_root_model__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "pydantic.main.BaseModel.__pydantic_root_model__", "name": "__pydantic_root_model__", "setter_type": null, "type": "builtins.bool"}}, "__pydantic_serializer__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "pydantic.main.BaseModel.__pydantic_serializer__", "name": "__pydantic_serializer__", "setter_type": null, "type": "pydantic_core._pydantic_core.SchemaSerializer"}}, "__pydantic_setattr_handlers__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "pydantic.main.BaseModel.__pydantic_setattr_handlers__", "name": "__pydantic_setattr_handlers__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["pydantic.main.BaseModel", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__pydantic_validator__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "pydantic.main.BaseModel.__pydantic_validator__", "name": "__pydantic_validator__", "setter_type": null, "type": {".class": "UnionType", "items": ["pydantic_core._pydantic_core.SchemaValidator", "pydantic.plugin._schema_validator.PluggableSchemaValidator"], "uses_pep604_syntax": true}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.main.BaseModel.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["pydantic.main.BaseModel"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__repr__ of BaseModel", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.main.BaseModel.__repr_args__", "name": "__repr_args__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.main.BaseModel"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__repr_args__ of BaseModel", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._repr.ReprArgs"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pydantic.main.BaseModel.__repr_name__", "name": "__repr_name__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic._internal._repr.Representation"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr_recursion__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pydantic.main.BaseModel.__repr_recursion__", "name": "__repr_recursion__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "object"], "arg_types": ["pydantic._internal._repr.Representation", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr_str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pydantic.main.BaseModel.__repr_str__", "name": "__repr_str__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "join_str"], "arg_types": ["pydantic._internal._repr.Representation", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__rich_repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pydantic.main.BaseModel.__rich_repr__", "name": "__rich_repr__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic._internal._repr.Representation"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._repr.RichReprResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__setstate__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "state"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.main.BaseModel.__setstate__", "name": "__setstate__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "state"], "arg_types": ["pydantic.main.BaseModel", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__setstate__ of BaseModel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__signature__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "pydantic.main.BaseModel.__signature__", "name": "__signature__", "setter_type": null, "type": "inspect.Signature"}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "pydantic.main.BaseModel.__slots__", "name": "__slots__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.main.BaseModel.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["pydantic.main.BaseModel"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__str__ of BaseModel", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_calculate_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": "function pydantic.main.BaseModel._calculate_keys is deprecated: The private method `_calculate_keys` will be removed and should no longer be used.", "flags": ["is_decorated", "is_trivial_self"], "fullname": "pydantic.main.BaseModel._calculate_keys", "name": "_calculate_keys", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["pydantic.main.BaseModel", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_calculate_keys of BaseModel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pydantic.main.BaseModel._calculate_keys", "name": "_calculate_keys", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["pydantic.main.BaseModel", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_calculate_keys of BaseModel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_copy_and_set_values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": "function pydantic.main.BaseModel._copy_and_set_values is deprecated: The private method `_copy_and_set_values` will be removed and should no longer be used.", "flags": ["is_decorated", "is_trivial_self"], "fullname": "pydantic.main.BaseModel._copy_and_set_values", "name": "_copy_and_set_values", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["pydantic.main.BaseModel", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_copy_and_set_values of BaseModel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pydantic.main.BaseModel._copy_and_set_values", "name": "_copy_and_set_values", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["pydantic.main.BaseModel", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_copy_and_set_values of BaseModel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["cls", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": "function pydantic.main.BaseModel._get_value is deprecated: The private method `_get_value` will be removed and should no longer be used.", "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "pydantic.main.BaseModel._get_value", "name": "_get_value", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["cls", "args", "kwargs"], "arg_types": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_value of BaseModel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic.main.BaseModel._get_value", "name": "_get_value", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["cls", "args", "kwargs"], "arg_types": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_value of BaseModel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_iter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": "function pydantic.main.BaseModel._iter is deprecated: The private method `_iter` will be removed and should no longer be used.", "flags": ["is_decorated", "is_trivial_self"], "fullname": "pydantic.main.BaseModel._iter", "name": "_iter", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["pydantic.main.BaseModel", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_iter of BaseModel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pydantic.main.BaseModel._iter", "name": "_iter", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["pydantic.main.BaseModel", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_iter of BaseModel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "construct": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["cls", "_fields_set", "values"], "dataclass_transform_spec": null, "deprecated": "function pydantic.main.BaseModel.construct is deprecated: The `construct` method is deprecated; use `model_construct` instead.", "flags": ["is_class", "is_decorated"], "fullname": "pydantic.main.BaseModel.construct", "name": "construct", "type": {".class": "CallableType", "arg_kinds": [0, 1, 4], "arg_names": ["cls", "_fields_set", "values"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "construct of BaseModel", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic.main.BaseModel.construct", "name": "construct", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 4], "arg_names": ["cls", "_fields_set", "values"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "construct of BaseModel", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}}}, "copy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["self", "include", "exclude", "update", "deep"], "dataclass_transform_spec": null, "deprecated": "function pydantic.main.BaseModel.copy is deprecated: The `copy` method is deprecated; use `model_copy` instead. See the docstring of `BaseModel.copy` for details about how to handle `include` and `exclude`.", "flags": ["is_decorated"], "fullname": "pydantic.main.BaseModel.copy", "name": "copy", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["self", "include", "exclude", "update", "deep"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._utils.AbstractSetIntStr"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._utils.MappingIntStrAny"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._utils.AbstractSetIntStr"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._utils.MappingIntStrAny"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "copy of BaseModel", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pydantic.main.BaseModel.copy", "name": "copy", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["self", "include", "exclude", "update", "deep"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._utils.AbstractSetIntStr"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._utils.MappingIntStrAny"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._utils.AbstractSetIntStr"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._utils.MappingIntStrAny"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "copy of BaseModel", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}}}, "dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "include", "exclude", "by_alias", "exclude_unset", "exclude_defaults", "exclude_none"], "dataclass_transform_spec": null, "deprecated": "function pydantic.main.BaseModel.dict is deprecated: The `dict` method is deprecated; use `model_dump` instead.", "flags": ["is_decorated", "is_trivial_self"], "fullname": "pydantic.main.BaseModel.dict", "name": "dict", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "include", "exclude", "by_alias", "exclude_unset", "exclude_defaults", "exclude_none"], "arg_types": ["pydantic.main.BaseModel", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.main.IncEx"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.main.IncEx"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "dict of BaseModel", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pydantic.main.BaseModel.dict", "name": "dict", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "include", "exclude", "by_alias", "exclude_unset", "exclude_defaults", "exclude_none"], "arg_types": ["pydantic.main.BaseModel", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.main.IncEx"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.main.IncEx"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "dict of BaseModel", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_orm": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "obj"], "dataclass_transform_spec": null, "deprecated": "function pydantic.main.BaseModel.from_orm is deprecated: The `from_orm` method is deprecated; set `model_config['from_attributes']=True` and use `model_validate` instead.", "flags": ["is_class", "is_decorated"], "fullname": "pydantic.main.BaseModel.from_orm", "name": "from_orm", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "obj"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_orm of BaseModel", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic.main.BaseModel.from_orm", "name": "from_orm", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "obj"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_orm of BaseModel", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}}}, "json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "include", "exclude", "by_alias", "exclude_unset", "exclude_defaults", "exclude_none", "encoder", "models_as_dict", "dumps_kwargs"], "dataclass_transform_spec": null, "deprecated": "function pydantic.main.BaseModel.json is deprecated: The `json` method is deprecated; use `model_dump_json` instead.", "flags": ["is_decorated", "is_trivial_self"], "fullname": "pydantic.main.BaseModel.json", "name": "json", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "include", "exclude", "by_alias", "exclude_unset", "exclude_defaults", "exclude_none", "encoder", "models_as_dict", "dumps_kwargs"], "arg_types": ["pydantic.main.BaseModel", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.main.IncEx"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.main.IncEx"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "json of BaseModel", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pydantic.main.BaseModel.json", "name": "json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "include", "exclude", "by_alias", "exclude_unset", "exclude_defaults", "exclude_none", "encoder", "models_as_dict", "dumps_kwargs"], "arg_types": ["pydantic.main.BaseModel", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.main.IncEx"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.main.IncEx"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "json of BaseModel", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "model_computed_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "pydantic.main.BaseModel.model_computed_fields", "name": "model_computed_fields", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_computed_fields of BaseModel", "ret_type": {".class": "Instance", "args": ["builtins.str", "pydantic.fields.ComputedFieldInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic.main.BaseModel.model_computed_fields", "name": "model_computed_fields", "setter_type": null, "type": {".class": "Instance", "args": ["pydantic.main.BaseModel", {".class": "Instance", "args": ["builtins.str", "pydantic.fields.ComputedFieldInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "pydantic._internal._utils.deprecated_instance_property"}}}}, "model_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "pydantic.main.BaseModel.model_config", "name": "model_config", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.config.ConfigDict"}}}, "model_construct": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["cls", "_fields_set", "values"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "pydantic.main.BaseModel.model_construct", "name": "model_construct", "type": {".class": "CallableType", "arg_kinds": [0, 1, 4], "arg_names": ["cls", "_fields_set", "values"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_construct of BaseModel", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic.main.BaseModel.model_construct", "name": "model_construct", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 4], "arg_names": ["cls", "_fields_set", "values"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_construct of BaseModel", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}}}, "model_copy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["self", "update", "deep"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.main.BaseModel.model_copy", "name": "model_copy", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["self", "update", "deep"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_copy of BaseModel", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}}, "model_dump": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "mode", "include", "exclude", "context", "by_alias", "exclude_unset", "exclude_defaults", "exclude_none", "round_trip", "warnings", "fallback", "serialize_as_any"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.main.BaseModel.model_dump", "name": "model_dump", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "mode", "include", "exclude", "context", "by_alias", "exclude_unset", "exclude_defaults", "exclude_none", "round_trip", "warnings", "fallback", "serialize_as_any"], "arg_types": ["pydantic.main.BaseModel", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "json"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "python"}, "builtins.str"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.main.IncEx"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.main.IncEx"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "LiteralType", "fallback": "builtins.str", "value": "none"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "warn"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "error"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_dump of BaseModel", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "model_dump_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "indent", "include", "exclude", "context", "by_alias", "exclude_unset", "exclude_defaults", "exclude_none", "round_trip", "warnings", "fallback", "serialize_as_any"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.main.BaseModel.model_dump_json", "name": "model_dump_json", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "indent", "include", "exclude", "context", "by_alias", "exclude_unset", "exclude_defaults", "exclude_none", "round_trip", "warnings", "fallback", "serialize_as_any"], "arg_types": ["pydantic.main.BaseModel", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.main.IncEx"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.main.IncEx"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "LiteralType", "fallback": "builtins.str", "value": "none"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "warn"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "error"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_dump_json of BaseModel", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "model_extra": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "pydantic.main.BaseModel.model_extra", "name": "model_extra", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.main.BaseModel"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_extra of BaseModel", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "pydantic.main.BaseModel.model_extra", "name": "model_extra", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.main.BaseModel"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_extra of BaseModel", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "model_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "pydantic.main.BaseModel.model_fields", "name": "model_fields", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_fields of BaseModel", "ret_type": {".class": "Instance", "args": ["builtins.str", "pydantic.fields.FieldInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic.main.BaseModel.model_fields", "name": "model_fields", "setter_type": null, "type": {".class": "Instance", "args": ["pydantic.main.BaseModel", {".class": "Instance", "args": ["builtins.str", "pydantic.fields.FieldInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "pydantic._internal._utils.deprecated_instance_property"}}}}, "model_fields_set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "pydantic.main.BaseModel.model_fields_set", "name": "model_fields_set", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.main.BaseModel"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_fields_set of BaseModel", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "pydantic.main.BaseModel.model_fields_set", "name": "model_fields_set", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic.main.BaseModel"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_fields_set of BaseModel", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "model_json_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["cls", "by_alias", "ref_template", "schema_generator", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "pydantic.main.BaseModel.model_json_schema", "name": "model_json_schema", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["cls", "by_alias", "ref_template", "schema_generator", "mode"], "arg_types": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}, "builtins.bool", "builtins.str", {".class": "TypeType", "item": "pydantic.json_schema.GenerateJsonSchema"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaMode"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_json_schema of BaseModel", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic.main.BaseModel.model_json_schema", "name": "model_json_schema", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["cls", "by_alias", "ref_template", "schema_generator", "mode"], "arg_types": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}, "builtins.bool", "builtins.str", {".class": "TypeType", "item": "pydantic.json_schema.GenerateJsonSchema"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaMode"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_json_schema of BaseModel", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "model_parametrized_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "pydantic.main.BaseModel.model_parametrized_name", "name": "model_parametrized_name", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "params"], "arg_types": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}, {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_parametrized_name of BaseModel", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic.main.BaseModel.model_parametrized_name", "name": "model_parametrized_name", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "params"], "arg_types": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}, {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_parametrized_name of BaseModel", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "model_post_init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "pydantic.main.BaseModel.model_post_init", "name": "model_post_init", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["pydantic.main.BaseModel", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_post_init of BaseModel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "model_rebuild": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["cls", "force", "raise_errors", "_parent_namespace_depth", "_types_namespace"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "pydantic.main.BaseModel.model_rebuild", "name": "model_rebuild", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["cls", "force", "raise_errors", "_parent_namespace_depth", "_types_namespace"], "arg_types": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}, "builtins.bool", "builtins.bool", "builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.MappingNamespace"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_rebuild of BaseModel", "ret_type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic.main.BaseModel.model_rebuild", "name": "model_rebuild", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["cls", "force", "raise_errors", "_parent_namespace_depth", "_types_namespace"], "arg_types": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}, "builtins.bool", "builtins.bool", "builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._namespace_utils.MappingNamespace"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_rebuild of BaseModel", "ret_type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "model_validate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5, 5], "arg_names": ["cls", "obj", "strict", "from_attributes", "context", "by_alias", "by_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "pydantic.main.BaseModel.model_validate", "name": "model_validate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5], "arg_names": ["cls", "obj", "strict", "from_attributes", "context", "by_alias", "by_name"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_validate of BaseModel", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic.main.BaseModel.model_validate", "name": "model_validate", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5], "arg_names": ["cls", "obj", "strict", "from_attributes", "context", "by_alias", "by_name"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_validate of BaseModel", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}}}, "model_validate_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["cls", "json_data", "strict", "context", "by_alias", "by_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "pydantic.main.BaseModel.model_validate_json", "name": "model_validate_json", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["cls", "json_data", "strict", "context", "by_alias", "by_name"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", "builtins.bytearray"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_validate_json of BaseModel", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic.main.BaseModel.model_validate_json", "name": "model_validate_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["cls", "json_data", "strict", "context", "by_alias", "by_name"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", "builtins.bytearray"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_validate_json of BaseModel", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}}}, "model_validate_strings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["cls", "obj", "strict", "context", "by_alias", "by_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "pydantic.main.BaseModel.model_validate_strings", "name": "model_validate_strings", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["cls", "obj", "strict", "context", "by_alias", "by_name"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_validate_strings of BaseModel", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic.main.BaseModel.model_validate_strings", "name": "model_validate_strings", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["cls", "obj", "strict", "context", "by_alias", "by_name"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "model_validate_strings of BaseModel", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}}}, "parse_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["cls", "path", "content_type", "encoding", "proto", "allow_pickle"], "dataclass_transform_spec": null, "deprecated": "function pydantic.main.BaseModel.parse_file is deprecated: The `parse_file` method is deprecated; load the data from file, then if your data is JSON use `model_validate_json`, otherwise `model_validate` instead.", "flags": ["is_class", "is_decorated"], "fullname": "pydantic.main.BaseModel.parse_file", "name": "parse_file", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["cls", "path", "content_type", "encoding", "proto", "allow_pickle"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["builtins.str", "pathlib.Path"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": ["pydantic.deprecated.parse.Protocol", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse_file of BaseModel", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic.main.BaseModel.parse_file", "name": "parse_file", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["cls", "path", "content_type", "encoding", "proto", "allow_pickle"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["builtins.str", "pathlib.Path"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": ["pydantic.deprecated.parse.Protocol", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse_file of BaseModel", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}}}, "parse_obj": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "obj"], "dataclass_transform_spec": null, "deprecated": "function pydantic.main.BaseModel.parse_obj is deprecated: The `parse_obj` method is deprecated; use `model_validate` instead.", "flags": ["is_class", "is_decorated"], "fullname": "pydantic.main.BaseModel.parse_obj", "name": "parse_obj", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "obj"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse_obj of BaseModel", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic.main.BaseModel.parse_obj", "name": "parse_obj", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "obj"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse_obj of BaseModel", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}}}, "parse_raw": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["cls", "b", "content_type", "encoding", "proto", "allow_pickle"], "dataclass_transform_spec": null, "deprecated": "function pydantic.main.BaseModel.parse_raw is deprecated: The `parse_raw` method is deprecated; if your data is JSON use `model_validate_json`, otherwise load the data then use `model_validate` instead.", "flags": ["is_class", "is_decorated"], "fullname": "pydantic.main.BaseModel.parse_raw", "name": "parse_raw", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["cls", "b", "content_type", "encoding", "proto", "allow_pickle"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": ["pydantic.deprecated.parse.Protocol", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse_raw of BaseModel", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic.main.BaseModel.parse_raw", "name": "parse_raw", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["cls", "b", "content_type", "encoding", "proto", "allow_pickle"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": ["pydantic.deprecated.parse.Protocol", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse_raw of BaseModel", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}}}, "schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["cls", "by_alias", "ref_template"], "dataclass_transform_spec": null, "deprecated": "function pydantic.main.BaseModel.schema is deprecated: The `schema` method is deprecated; use `model_json_schema` instead.", "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "pydantic.main.BaseModel.schema", "name": "schema", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["cls", "by_alias", "ref_template"], "arg_types": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}, "builtins.bool", "builtins.str"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "schema of BaseModel", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic.main.BaseModel.schema", "name": "schema", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["cls", "by_alias", "ref_template"], "arg_types": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}, "builtins.bool", "builtins.str"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "schema of BaseModel", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "schema_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 4], "arg_names": ["cls", "by_alias", "ref_template", "dumps_kwargs"], "dataclass_transform_spec": null, "deprecated": "function pydantic.main.BaseModel.schema_json is deprecated: The `schema_json` method is deprecated; use `model_json_schema` and json.dumps instead.", "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "pydantic.main.BaseModel.schema_json", "name": "schema_json", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 4], "arg_names": ["cls", "by_alias", "ref_template", "dumps_kwargs"], "arg_types": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}, "builtins.bool", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "schema_json of BaseModel", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic.main.BaseModel.schema_json", "name": "schema_json", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 4], "arg_names": ["cls", "by_alias", "ref_template", "dumps_kwargs"], "arg_types": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}, "builtins.bool", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "schema_json of BaseModel", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "update_forward_refs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["cls", "localns"], "dataclass_transform_spec": null, "deprecated": "function pydantic.main.BaseModel.update_forward_refs is deprecated: The `update_forward_refs` method is deprecated; use `model_rebuild` instead.", "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "pydantic.main.BaseModel.update_forward_refs", "name": "update_forward_refs", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["cls", "localns"], "arg_types": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_forward_refs of BaseModel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic.main.BaseModel.update_forward_refs", "name": "update_forward_refs", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["cls", "localns"], "arg_types": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_forward_refs of BaseModel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "validate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "value"], "dataclass_transform_spec": null, "deprecated": "function pydantic.main.BaseModel.validate is deprecated: The `validate` method is deprecated; use `model_validate` instead.", "flags": ["is_class", "is_decorated"], "fullname": "pydantic.main.BaseModel.validate", "name": "validate", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "value"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "validate of BaseModel", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic.main.BaseModel.validate", "name": "validate", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "value"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "validate of BaseModel", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.BaseModel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef", "module_public": false}, "ComputedFieldInfo": {".class": "SymbolTableNode", "cross_ref": "pydantic.fields.ComputedFieldInfo", "kind": "Gdef", "module_public": false}, "ConfigDict": {".class": "SymbolTableNode", "cross_ref": "pydantic.config.ConfigDict", "kind": "Gdef", "module_public": false}, "CoreSchema": {".class": "SymbolTableNode", "cross_ref": "pydantic_core.core_schema.CoreSchema", "kind": "Gdef", "module_public": false}, "DEFAULT_REF_TEMPLATE": {".class": "SymbolTableNode", "cross_ref": "pydantic.json_schema.DEFAULT_REF_TEMPLATE", "kind": "Gdef", "module_public": false}, "DeprecatedParseProtocol": {".class": "SymbolTableNode", "cross_ref": "pydantic.deprecated.parse.Protocol", "kind": "Gdef", "module_public": false}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_public": false}, "FieldInfo": {".class": "SymbolTableNode", "cross_ref": "pydantic.fields.FieldInfo", "kind": "Gdef", "module_public": false}, "GenerateJsonSchema": {".class": "SymbolTableNode", "cross_ref": "pydantic.json_schema.GenerateJsonSchema", "kind": "Gdef", "module_public": false}, "Generator": {".class": "SymbolTableNode", "cross_ref": "typing.Generator", "kind": "Gdef", "module_public": false}, "GetCoreSchemaHandler": {".class": "SymbolTableNode", "cross_ref": "pydantic.annotated_handlers.GetCoreSchemaHandler", "kind": "Gdef", "module_public": false}, "GetJsonSchemaHandler": {".class": "SymbolTableNode", "cross_ref": "pydantic.annotated_handlers.GetJsonSchemaHandler", "kind": "Gdef", "module_public": false}, "IncEx": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic.main.IncEx", "line": 79, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "Instance", "args": ["builtins.int", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.main.IncEx"}, "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.main.IncEx"}, "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "uses_pep604_syntax": false}}}, "JsonSchemaMode": {".class": "SymbolTableNode", "cross_ref": "pydantic.json_schema.JsonSchemaMode", "kind": "Gdef", "module_public": false}, "JsonSchemaValue": {".class": "SymbolTableNode", "cross_ref": "pydantic.json_schema.JsonSchemaValue", "kind": "Gdef", "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_public": false}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_public": false}, "MappingIntStrAny": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._utils.MappingIntStrAny", "kind": "Gdef", "module_public": false}, "MappingNamespace": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._namespace_utils.MappingNamespace", "kind": "Gdef", "module_public": false}, "ModelPrivateAttr": {".class": "SymbolTableNode", "cross_ref": "pydantic.fields.ModelPrivateAttr", "kind": "Gdef", "module_public": false}, "ModelT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.ModelT", "name": "ModelT", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef", "module_public": false}, "PluggableSchemaValidator": {".class": "SymbolTableNode", "cross_ref": "pydantic.plugin._schema_validator.PluggableSchemaValidator", "kind": "Gdef", "module_public": false}, "PydanticDeprecatedSince20": {".class": "SymbolTableNode", "cross_ref": "pydantic.warnings.PydanticDeprecatedSince20", "kind": "Gdef", "module_public": false}, "PydanticDeprecatedSince211": {".class": "SymbolTableNode", "cross_ref": "pydantic.warnings.PydanticDeprecatedSince211", "kind": "Gdef", "module_public": false}, "PydanticUndefined": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.PydanticUndefined", "kind": "Gdef", "module_public": false}, "PydanticUndefinedAnnotation": {".class": "SymbolTableNode", "cross_ref": "pydantic.errors.PydanticUndefinedAnnotation", "kind": "Gdef", "module_public": false}, "PydanticUserError": {".class": "SymbolTableNode", "cross_ref": "pydantic.errors.PydanticUserError", "kind": "Gdef", "module_public": false}, "SchemaSerializer": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.SchemaSerializer", "kind": "Gdef", "module_public": false}, "SchemaValidator": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.SchemaValidator", "kind": "Gdef", "module_public": false}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing.Self", "kind": "Gdef", "module_public": false}, "Signature": {".class": "SymbolTableNode", "cross_ref": "inspect.Signature", "kind": "Gdef", "module_public": false}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "TupleGenerator": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic.main.TupleGenerator", "line": 75, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}}}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef", "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "Unpack": {".class": "SymbolTableNode", "cross_ref": "typing.Unpack", "kind": "Gdef", "module_public": false}, "ValidationError": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.ValidationError", "kind": "Gdef", "module_public": false}, "_SIMPLE_SETATTR_HANDLERS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "pydantic.main._SIMPLE_SETATTR_HANDLERS", "name": "_SIMPLE_SETATTR_HANDLERS", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["pydantic.main.BaseModel", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "typing.Mapping"}}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic.main.__all__", "name": "__all__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.main.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.main.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.main.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__getattr__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic.main.__getattr__", "name": "__getattr__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.main.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.main.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic.main.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "_check_frozen": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["model_cls", "name", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.main._check_frozen", "name": "_check_frozen", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["model_cls", "name", "value"], "arg_types": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_check_frozen", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_config": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._config", "kind": "Gdef", "module_public": false}, "_decorators": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._decorators", "kind": "Gdef", "module_public": false}, "_fields": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._fields", "kind": "Gdef", "module_public": false}, "_forward_ref": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._forward_ref", "kind": "Gdef", "module_public": false}, "_generics": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._generics", "kind": "Gdef", "module_public": false}, "_mock_val_ser": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._mock_val_ser", "kind": "Gdef", "module_public": false}, "_model_construction": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._model_construction", "kind": "Gdef", "module_public": false}, "_model_field_setattr_handler": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["model", "name", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.main._model_field_setattr_handler", "name": "_model_field_setattr_handler", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["model", "name", "val"], "arg_types": ["pydantic.main.BaseModel", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_model_field_setattr_handler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_namespace_utils": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._namespace_utils", "kind": "Gdef", "module_public": false}, "_object_setattr": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic.main._object_setattr", "name": "_object_setattr", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["builtins.object", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_private_setattr_handler": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["model", "name", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic.main._private_setattr_handler", "name": "_private_setattr_handler", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["model", "name", "val"], "arg_types": ["pydantic.main.BaseModel", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_private_setattr_handler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_repr": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._repr", "kind": "Gdef", "module_public": false}, "_typing_extra": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._typing_extra", "kind": "Gdef", "module_public": false}, "_utils": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._utils", "kind": "Gdef", "module_public": false}, "cached_property": {".class": "SymbolTableNode", "cross_ref": "functools.cached_property", "kind": "Gdef", "module_public": false}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef", "module_public": false}, "copy": {".class": "SymbolTableNode", "cross_ref": "copy.copy", "kind": "Gdef", "module_public": false}, "create_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "pydantic.main.create_model", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 4], "arg_names": [null, "__config__", "__doc__", "__base__", "__module__", "__validators__", "__cls_kwargs__", "field_definitions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "pydantic.main.create_model", "name": "create_model", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 4], "arg_names": [null, "__config__", "__doc__", "__base__", "__module__", "__validators__", "__cls_kwargs__", "field_definitions"], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.config.ConfigDict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.ModelT", "id": -1, "name": "ModelT", "namespace": "pydantic.main.create_model", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.ModelT", "id": -1, "name": "ModelT", "namespace": "pydantic.main.create_model", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_model", "ret_type": {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.ModelT", "id": -1, "name": "ModelT", "namespace": "pydantic.main.create_model", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.ModelT", "id": -1, "name": "ModelT", "namespace": "pydantic.main.create_model", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 4], "arg_names": [null, "__config__", "__doc__", "__base__", "__module__", "__validators__", "__cls_kwargs__", "field_definitions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "pydantic.main.create_model", "name": "create_model", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 4], "arg_names": [null, "__config__", "__doc__", "__base__", "__module__", "__validators__", "__cls_kwargs__", "field_definitions"], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.config.ConfigDict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "NoneType"}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_model", "ret_type": {".class": "TypeType", "item": "pydantic.main.BaseModel"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "pydantic.main.create_model", "name": "create_model", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 4], "arg_names": [null, "__config__", "__doc__", "__base__", "__module__", "__validators__", "__cls_kwargs__", "field_definitions"], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.config.ConfigDict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "NoneType"}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_model", "ret_type": {".class": "TypeType", "item": "pydantic.main.BaseModel"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 3, 5, 5, 5, 4], "arg_names": [null, "__config__", "__doc__", "__base__", "__module__", "__validators__", "__cls_kwargs__", "field_definitions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "pydantic.main.create_model", "name": "create_model", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 3, 5, 5, 5, 4], "arg_names": [null, "__config__", "__doc__", "__base__", "__module__", "__validators__", "__cls_kwargs__", "field_definitions"], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.config.ConfigDict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.ModelT", "id": -1, "name": "ModelT", "namespace": "pydantic.main.create_model#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.ModelT", "id": -1, "name": "ModelT", "namespace": "pydantic.main.create_model#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_model", "ret_type": {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.ModelT", "id": -1, "name": "ModelT", "namespace": "pydantic.main.create_model#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.ModelT", "id": -1, "name": "ModelT", "namespace": "pydantic.main.create_model#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "pydantic.main.create_model", "name": "create_model", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 3, 5, 5, 5, 4], "arg_names": [null, "__config__", "__doc__", "__base__", "__module__", "__validators__", "__cls_kwargs__", "field_definitions"], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.config.ConfigDict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.ModelT", "id": -1, "name": "ModelT", "namespace": "pydantic.main.create_model#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.ModelT", "id": -1, "name": "ModelT", "namespace": "pydantic.main.create_model#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_model", "ret_type": {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.ModelT", "id": -1, "name": "ModelT", "namespace": "pydantic.main.create_model#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.ModelT", "id": -1, "name": "ModelT", "namespace": "pydantic.main.create_model#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 4], "arg_names": [null, "__config__", "__doc__", "__base__", "__module__", "__validators__", "__cls_kwargs__", "field_definitions"], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.config.ConfigDict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "NoneType"}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_model", "ret_type": {".class": "TypeType", "item": "pydantic.main.BaseModel"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 5, 5, 3, 5, 5, 5, 4], "arg_names": [null, "__config__", "__doc__", "__base__", "__module__", "__validators__", "__cls_kwargs__", "field_definitions"], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.config.ConfigDict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.ModelT", "id": -1, "name": "ModelT", "namespace": "pydantic.main.create_model#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.ModelT", "id": -1, "name": "ModelT", "namespace": "pydantic.main.create_model#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_model", "ret_type": {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.ModelT", "id": -1, "name": "ModelT", "namespace": "pydantic.main.create_model#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic.main.ModelT", "id": -1, "name": "ModelT", "namespace": "pydantic.main.create_model#1", "upper_bound": "pydantic.main.BaseModel", "values": [], "variance": 0}]}]}}}, "deepcopy": {".class": "SymbolTableNode", "cross_ref": "copy.deepcopy", "kind": "Gdef", "module_public": false}, "getattr_migration": {".class": "SymbolTableNode", "cross_ref": "pydantic._migration.getattr_migration", "kind": "Gdef", "module_public": false}, "model_json_schema": {".class": "SymbolTableNode", "cross_ref": "pydantic.json_schema.model_json_schema", "kind": "Gdef", "module_public": false}, "operator": {".class": "SymbolTableNode", "cross_ref": "operator", "kind": "Gdef", "module_public": false}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_public": false}, "pydantic_core": {".class": "SymbolTableNode", "cross_ref": "pydantic_core", "kind": "Gdef", "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_public": false}, "types": {".class": "SymbolTableNode", "cross_ref": "types", "kind": "Gdef", "module_public": false}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_public": false}, "typing_extensions": {".class": "SymbolTableNode", "cross_ref": "typing_extensions", "kind": "Gdef", "module_public": false}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\Documents\\GitHub\\workshop\\.conda\\Lib\\site-packages\\pydantic\\main.py"}