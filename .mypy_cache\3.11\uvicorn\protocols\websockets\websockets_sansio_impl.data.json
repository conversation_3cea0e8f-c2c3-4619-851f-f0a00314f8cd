{".class": "MypyFile", "_fullname": "uvicorn.protocols.websockets.websockets_sansio_impl", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ASGIReceiveEvent": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.ASGIReceiveEvent", "kind": "Gdef"}, "ASGISendEvent": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.ASGISendEvent", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BaseTransport": {".class": "SymbolTableNode", "cross_ref": "asyncio.transports.BaseTransport", "kind": "Gdef"}, "ClientDisconnected": {".class": "SymbolTableNode", "cross_ref": "uvicorn.protocols.utils.ClientDisconnected", "kind": "Gdef"}, "Config": {".class": "SymbolTableNode", "cross_ref": "uvicorn.config.Config", "kind": "Gdef"}, "Frame": {".class": "SymbolTableNode", "cross_ref": "websockets.frames.Frame", "kind": "Gdef"}, "HTTPStatus": {".class": "SymbolTableNode", "cross_ref": "http.HTTPStatus", "kind": "Gdef"}, "InvalidState": {".class": "SymbolTableNode", "cross_ref": "websockets.exceptions.InvalidState", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "Opcode": {".class": "SymbolTableNode", "cross_ref": "websockets.frames.Opcode", "kind": "Gdef"}, "Request": {".class": "SymbolTableNode", "cross_ref": "websockets.http11.Request", "kind": "Gdef"}, "ServerPerMessageDeflateFactory": {".class": "SymbolTableNode", "cross_ref": "websockets.extensions.permessage_deflate.ServerPerMessageDeflateFactory", "kind": "Gdef"}, "ServerProtocol": {".class": "SymbolTableNode", "cross_ref": "websockets.server.ServerProtocol", "kind": "Gdef"}, "ServerState": {".class": "SymbolTableNode", "cross_ref": "uvicorn.server.ServerState", "kind": "Gdef"}, "TRACE_LOG_LEVEL": {".class": "SymbolTableNode", "cross_ref": "uvicorn.logging.TRACE_LOG_LEVEL", "kind": "Gdef"}, "Transport": {".class": "SymbolTableNode", "cross_ref": "asyncio.transports.Transport", "kind": "Gdef"}, "WebSocketAcceptEvent": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.WebSocketAcceptEvent", "kind": "Gdef"}, "WebSocketCloseEvent": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.WebSocketCloseEvent", "kind": "Gdef"}, "WebSocketResponseBodyEvent": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.WebSocketResponseBodyEvent", "kind": "Gdef"}, "WebSocketResponseStartEvent": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.WebSocketResponseStartEvent", "kind": "Gdef"}, "WebSocketScope": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.WebSocketScope", "kind": "Gdef"}, "WebSocketSendEvent": {".class": "SymbolTableNode", "cross_ref": "uvicorn._types.WebSocketSendEvent", "kind": "Gdef"}, "WebSocketsSansIOProtocol": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["asyncio.protocols.Protocol"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol", "name": "WebSocketsSansIOProtocol", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "uvicorn.protocols.websockets.websockets_sansio_impl", "mro": ["uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol", "asyncio.protocols.Protocol", "asyncio.protocols.BaseProtocol", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "config", "server_state", "app_state", "_loop"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "config", "server_state", "app_state", "_loop"], "arg_types": ["uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol", "uvicorn.config.Config", "uvicorn.server.ServerState", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": ["asyncio.events.AbstractEventLoop", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of WebSocketsSansIOProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "app": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol.app", "name": "app", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "app_state": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol.app_state", "name": "app_state", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "bytes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol.bytes", "name": "bytes", "setter_type": null, "type": "builtins.bytes"}}, "client": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol.client", "name": "client", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "close_sent": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol.close_sent", "name": "close_sent", "setter_type": null, "type": "builtins.bool"}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol.config", "name": "config", "setter_type": null, "type": "uvicorn.config.Config"}}, "conn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol.conn", "name": "conn", "setter_type": null, "type": "websockets.server.ServerProtocol"}}, "connection_lost": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "exc"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol.connection_lost", "name": "connection_lost", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "exc"], "arg_types": ["uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol", {".class": "UnionType", "items": ["builtins.Exception", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "connection_lost of WebSocketsSansIOProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "connection_made": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "transport"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol.connection_made", "name": "connection_made", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "transport"], "arg_types": ["uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol", "asyncio.transports.BaseTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "connection_made of WebSocketsSansIOProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "connections": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol.connections", "name": "connections", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "uvicorn.server.Protocols"}], "extra_attrs": null, "type_ref": "builtins.set"}}}, "curr_msg_data_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol.curr_msg_data_type", "name": "curr_msg_data_type", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bytes"}], "uses_pep604_syntax": false}}}, "data_received": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol.data_received", "name": "data_received", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol", "builtins.bytes"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "data_received of WebSocketsSansIOProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "default_headers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol.default_headers", "name": "default_headers", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "eof_received": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol.eof_received", "name": "eof_received", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "eof_received of WebSocketsSansIOProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_bytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol.handle_bytes", "name": "handle_bytes", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event"], "arg_types": ["uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol", "websockets.frames.Frame"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "handle_bytes of WebSocketsSansIOProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol.handle_close", "name": "handle_close", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event"], "arg_types": ["uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol", "websockets.frames.Frame"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "handle_close of WebSocketsSansIOProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_connect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol.handle_connect", "name": "handle_connect", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event"], "arg_types": ["uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol", "websockets.http11.Request"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "handle_connect of WebSocketsSansIOProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_cont": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol.handle_cont", "name": "handle_cont", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event"], "arg_types": ["uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol", "websockets.frames.Frame"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "handle_cont of WebSocketsSansIOProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_events": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol.handle_events", "name": "handle_events", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "handle_events of WebSocketsSansIOProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_parser_exception": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol.handle_parser_exception", "name": "handle_parser_exception", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "handle_parser_exception of WebSocketsSansIOProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_ping": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol.handle_ping", "name": "handle_ping", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "handle_ping of WebSocketsSansIOProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol.handle_text", "name": "handle_text", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event"], "arg_types": ["uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol", "websockets.frames.Frame"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "handle_text of WebSocketsSansIOProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handshake_complete": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol.handshake_complete", "name": "handshake_complete", "setter_type": null, "type": "builtins.bool"}}, "handshake_initiated": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol.handshake_initiated", "name": "handshake_initiated", "setter_type": null, "type": "builtins.bool"}}, "initial_response": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol.initial_response", "name": "initial_response", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "logger": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "loop": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol.loop", "name": "loop", "setter_type": null, "type": "asyncio.events.AbstractEventLoop"}}, "on_task_complete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "task"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol.on_task_complete", "name": "on_task_complete", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "task"], "arg_types": ["uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol", {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "_asyncio.Task"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_task_complete of WebSocketsSansIOProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "queue": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol.queue", "name": "queue", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.ASGIReceiveEvent"}], "extra_attrs": null, "type_ref": "asyncio.queues.Queue"}}}, "read_paused": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol.read_paused", "name": "read_paused", "setter_type": null, "type": "builtins.bool"}}, "receive": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol.receive", "name": "receive", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "receive of WebSocketsSansIOProtocol", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.ASGIReceiveEvent"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "request": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol.request", "name": "request", "setter_type": null, "type": "websockets.http11.Request"}}, "response": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol.response", "name": "response", "setter_type": null, "type": "websockets.http11.Response"}}, "root_path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol.root_path", "name": "root_path", "setter_type": null, "type": "builtins.str"}}, "run_asgi": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol.run_asgi", "name": "run_asgi", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "run_asgi of WebSocketsSansIOProtocol", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "scheme": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol.scheme", "name": "scheme", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "wss"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ws"}], "uses_pep604_syntax": false}}}, "scope": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol.scope", "name": "scope", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.WebSocketScope"}}}, "send": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "message"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol.send", "name": "send", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "message"], "arg_types": ["uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol", {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.ASGISendEvent"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "send of WebSocketsSansIOProtocol", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "send_500_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol.send_500_response", "name": "send_500_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "send_500_response of WebSocketsSansIOProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "send_receive_event_to_app": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol.send_receive_event_to_app", "name": "send_receive_event_to_app", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "send_receive_event_to_app of WebSocketsSansIOProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "server": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol.server", "name": "server", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "shutdown": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol.shutdown", "name": "shutdown", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "shutdown of WebSocketsSansIOProtocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tasks": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol.tasks", "name": "tasks", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "_asyncio.Task"}], "extra_attrs": null, "type_ref": "builtins.set"}}}, "transport": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol.transport", "name": "transport", "setter_type": null, "type": "asyncio.transports.Transport"}}, "writable": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol.writable", "name": "writable", "setter_type": null, "type": "asyncio.locks.Event"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "uvicorn.protocols.websockets.websockets_sansio_impl.WebSocketsSansIOProtocol", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "uvicorn.protocols.websockets.websockets_sansio_impl.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "uvicorn.protocols.websockets.websockets_sansio_impl.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "uvicorn.protocols.websockets.websockets_sansio_impl.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "uvicorn.protocols.websockets.websockets_sansio_impl.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "uvicorn.protocols.websockets.websockets_sansio_impl.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "uvicorn.protocols.websockets.websockets_sansio_impl.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "assert_never": {".class": "SymbolTableNode", "cross_ref": "typing.assert_never", "kind": "Gdef"}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "get_local_addr": {".class": "SymbolTableNode", "cross_ref": "uvicorn.protocols.utils.get_local_addr", "kind": "Gdef"}, "get_path_with_query_string": {".class": "SymbolTableNode", "cross_ref": "uvicorn.protocols.utils.get_path_with_query_string", "kind": "Gdef"}, "get_remote_addr": {".class": "SymbolTableNode", "cross_ref": "uvicorn.protocols.utils.get_remote_addr", "kind": "Gdef"}, "is_ssl": {".class": "SymbolTableNode", "cross_ref": "uvicorn.protocols.utils.is_ssl", "kind": "Gdef"}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "unquote": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.unquote", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\Documents\\GitHub\\workshop\\.conda\\Lib\\site-packages\\uvicorn\\protocols\\websockets\\websockets_sansio_impl.py"}