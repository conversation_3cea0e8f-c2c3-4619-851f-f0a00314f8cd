{".class": "MypyFile", "_fullname": "requests", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "ConnectTimeout": {".class": "SymbolTableNode", "cross_ref": "requests.exceptions.ConnectTimeout", "kind": "Gdef"}, "ConnectionError": {".class": "SymbolTableNode", "cross_ref": "requests.exceptions.ConnectionError", "kind": "Gdef"}, "FileModeWarning": {".class": "SymbolTableNode", "cross_ref": "requests.exceptions.FileModeWarning", "kind": "Gdef"}, "HTTPError": {".class": "SymbolTableNode", "cross_ref": "requests.exceptions.HTTPError", "kind": "Gdef"}, "JSONDecodeError": {".class": "SymbolTableNode", "cross_ref": "requests.exceptions.JSONDecodeError", "kind": "Gdef"}, "PreparedRequest": {".class": "SymbolTableNode", "cross_ref": "requests.models.PreparedRequest", "kind": "Gdef"}, "ReadTimeout": {".class": "SymbolTableNode", "cross_ref": "requests.exceptions.ReadTimeout", "kind": "Gdef"}, "Request": {".class": "SymbolTableNode", "cross_ref": "requests.models.Request", "kind": "Gdef"}, "RequestException": {".class": "SymbolTableNode", "cross_ref": "requests.exceptions.RequestException", "kind": "Gdef"}, "Response": {".class": "SymbolTableNode", "cross_ref": "requests.models.Response", "kind": "Gdef"}, "Session": {".class": "SymbolTableNode", "cross_ref": "requests.sessions.Session", "kind": "Gdef"}, "Timeout": {".class": "SymbolTableNode", "cross_ref": "requests.exceptions.Timeout", "kind": "Gdef"}, "TooManyRedirects": {".class": "SymbolTableNode", "cross_ref": "requests.exceptions.TooManyRedirects", "kind": "Gdef"}, "URLRequired": {".class": "SymbolTableNode", "cross_ref": "requests.exceptions.URLRequired", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "requests.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__author__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "requests.__author__", "name": "__author__", "setter_type": null, "type": "builtins.str"}}, "__author_email__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "requests.__author_email__", "name": "__author_email__", "setter_type": null, "type": "builtins.str"}}, "__build__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "requests.__build__", "name": "__build__", "setter_type": null, "type": "builtins.int"}}, "__cake__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "requests.__cake__", "name": "__cake__", "setter_type": null, "type": "builtins.str"}}, "__copyright__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "requests.__copyright__", "name": "__copyright__", "setter_type": null, "type": "builtins.str"}}, "__description__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "requests.__description__", "name": "__description__", "setter_type": null, "type": "builtins.str"}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "requests.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "requests.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__license__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "requests.__license__", "name": "__license__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "requests.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "requests.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "requests.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "requests.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "__title__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "requests.__title__", "name": "__title__", "setter_type": null, "type": "builtins.str"}}, "__url__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "requests.__url__", "name": "__url__", "setter_type": null, "type": "builtins.str"}}, "__version__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "requests.__version__", "name": "__version__", "setter_type": null, "type": "builtins.str"}}, "check_compatibility": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["urllib3_version", "chardet_version", "charset_normalizer_version"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "requests.check_compatibility", "name": "check_compatibility", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["urllib3_version", "chardet_version", "charset_normalizer_version"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "check_compatibility", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "codes": {".class": "SymbolTableNode", "cross_ref": "requests.status_codes.codes", "kind": "Gdef"}, "delete": {".class": "SymbolTableNode", "cross_ref": "requests.api.delete", "kind": "Gdef"}, "get": {".class": "SymbolTableNode", "cross_ref": "requests.api.get", "kind": "Gdef"}, "head": {".class": "SymbolTableNode", "cross_ref": "requests.api.head", "kind": "Gdef"}, "options": {".class": "SymbolTableNode", "cross_ref": "requests.api.options", "kind": "Gdef"}, "packages": {".class": "SymbolTableNode", "cross_ref": "requests.packages", "kind": "Gdef"}, "patch": {".class": "SymbolTableNode", "cross_ref": "requests.api.patch", "kind": "Gdef"}, "post": {".class": "SymbolTableNode", "cross_ref": "requests.api.post", "kind": "Gdef"}, "put": {".class": "SymbolTableNode", "cross_ref": "requests.api.put", "kind": "Gdef"}, "request": {".class": "SymbolTableNode", "cross_ref": "requests.api.request", "kind": "Gdef"}, "session": {".class": "SymbolTableNode", "cross_ref": "requests.sessions.session", "kind": "Gdef"}, "utils": {".class": "SymbolTableNode", "cross_ref": "requests.utils", "kind": "Gdef"}, "version_mod": {".class": "SymbolTableNode", "cross_ref": "requests.__version__", "kind": "Gdef", "module_public": false}}, "path": "C:\\Users\\<USER>\\Documents\\GitHub\\workshop\\.conda\\Lib\\site-packages\\requests-stubs\\__init__.pyi"}