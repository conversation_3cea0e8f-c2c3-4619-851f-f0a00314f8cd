{".class": "MypyFile", "_fullname": "wtforms.fields.simple", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BaseForm": {".class": "SymbolTableNode", "cross_ref": "wtforms.form.BaseForm", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BooleanField": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["wtforms.fields.core.Field"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "wtforms.fields.simple.BooleanField", "name": "BooleanField", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "wtforms.fields.simple.BooleanField", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "wtforms.fields.simple", "mro": ["wtforms.fields.simple.BooleanField", "wtforms.fields.core.Field", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "label", "validators", "false_values", "filters", "description", "id", "default", "widget", "render_kw", "name", "_form", "_prefix", "_translations", "_meta"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "wtforms.fields.simple.BooleanField.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "label", "validators", "false_values", "filters", "description", "id", "default", "widget", "render_kw", "name", "_form", "_prefix", "_translations", "_meta"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.simple.BooleanField.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.fields.simple.BooleanField", "values": [], "variance": 0}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.core._FormT", "id": -1, "name": "_FormT", "namespace": "wtforms.fields.simple.BooleanField.__init__", "upper_bound": "wtforms.form.BaseForm", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.simple.BooleanField.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.fields.simple.BooleanField", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "wtforms.fields.core._Validator"}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Collection"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "wtforms.fields.core._Filter"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.simple.BooleanField.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.fields.simple.BooleanField", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "wtforms.fields.core._Widget"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["wtforms.form.BaseForm", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": ["wtforms.meta._SupportsGettextAndNgettext", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["wtforms.meta.DefaultMeta", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of BooleanField", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.simple.BooleanField.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.fields.simple.BooleanField", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.core._FormT", "id": -1, "name": "_FormT", "namespace": "wtforms.fields.simple.BooleanField.__init__", "upper_bound": "wtforms.form.BaseForm", "values": [], "variance": 0}]}}}, "data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "wtforms.fields.simple.BooleanField.data", "name": "data", "setter_type": null, "type": "builtins.bool"}}, "default": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "wtforms.fields.simple.BooleanField.default", "name": "default", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "false_values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "wtforms.fields.simple.BooleanField.false_values", "name": "false_values", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Collection"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.simple.BooleanField.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.fields.simple.BooleanField", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Collection": {".class": "SymbolTableNode", "cross_ref": "typing.Collection", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ColorField": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["wtforms.fields.simple.StringField"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "wtforms.fields.simple.ColorField", "name": "ColorField", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "wtforms.fields.simple.ColorField", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "wtforms.fields.simple", "mro": ["wtforms.fields.simple.ColorField", "wtforms.fields.simple.StringField", "wtforms.fields.core.Field", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.simple.ColorField.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.fields.simple.ColorField", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DefaultMeta": {".class": "SymbolTableNode", "cross_ref": "wtforms.meta.DefaultMeta", "kind": "Gdef", "module_hidden": true, "module_public": false}, "EmailField": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["wtforms.fields.simple.StringField"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "wtforms.fields.simple.EmailField", "name": "EmailField", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "wtforms.fields.simple.EmailField", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "wtforms.fields.simple", "mro": ["wtforms.fields.simple.EmailField", "wtforms.fields.simple.StringField", "wtforms.fields.core.Field", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.simple.EmailField.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.fields.simple.EmailField", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Field": {".class": "SymbolTableNode", "cross_ref": "wtforms.fields.core.Field", "kind": "Gdef", "module_hidden": true, "module_public": false}, "FileField": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["wtforms.fields.core.Field"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "wtforms.fields.simple.FileField", "name": "FileField", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "wtforms.fields.simple.FileField", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "wtforms.fields.simple", "mro": ["wtforms.fields.simple.FileField", "wtforms.fields.core.Field", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.simple.FileField.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.fields.simple.FileField", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HiddenField": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["wtforms.fields.simple.StringField"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "wtforms.fields.simple.HiddenField", "name": "HiddenField", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "wtforms.fields.simple.HiddenField", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "wtforms.fields.simple", "mro": ["wtforms.fields.simple.HiddenField", "wtforms.fields.simple.StringField", "wtforms.fields.core.Field", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.simple.HiddenField.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.fields.simple.HiddenField", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MultipleFileField": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["wtforms.fields.simple.FileField"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "wtforms.fields.simple.MultipleFileField", "name": "MultipleFileField", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "wtforms.fields.simple.MultipleFileField", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "wtforms.fields.simple", "mro": ["wtforms.fields.simple.MultipleFileField", "wtforms.fields.simple.FileField", "wtforms.fields.core.Field", "builtins.object"], "names": {".class": "SymbolTable", "data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "wtforms.fields.simple.MultipleFileField.data", "name": "data", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.simple.MultipleFileField.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.fields.simple.MultipleFileField", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PasswordField": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["wtforms.fields.simple.StringField"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "wtforms.fields.simple.PasswordField", "name": "PasswordField", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "wtforms.fields.simple.PasswordField", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "wtforms.fields.simple", "mro": ["wtforms.fields.simple.PasswordField", "wtforms.fields.simple.StringField", "wtforms.fields.core.Field", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.simple.PasswordField.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.fields.simple.PasswordField", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SearchField": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["wtforms.fields.simple.StringField"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "wtforms.fields.simple.SearchField", "name": "SearchField", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "wtforms.fields.simple.SearchField", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "wtforms.fields.simple", "mro": ["wtforms.fields.simple.SearchField", "wtforms.fields.simple.StringField", "wtforms.fields.core.Field", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.simple.SearchField.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.fields.simple.SearchField", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing.Self", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_hidden": true, "module_public": false}, "StringField": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["wtforms.fields.core.Field"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "wtforms.fields.simple.StringField", "name": "StringField", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "wtforms.fields.simple.StringField", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "wtforms.fields.simple", "mro": ["wtforms.fields.simple.StringField", "wtforms.fields.core.Field", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "label", "validators", "filters", "description", "id", "default", "widget", "render_kw", "name", "_form", "_prefix", "_translations", "_meta"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "wtforms.fields.simple.StringField.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "label", "validators", "filters", "description", "id", "default", "widget", "render_kw", "name", "_form", "_prefix", "_translations", "_meta"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.simple.StringField.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.fields.simple.StringField", "values": [], "variance": 0}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.core._FormT", "id": -1, "name": "_FormT", "namespace": "wtforms.fields.simple.StringField.__init__", "upper_bound": "wtforms.form.BaseForm", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.simple.StringField.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.fields.simple.StringField", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "wtforms.fields.core._Validator"}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "wtforms.fields.core._Filter"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.simple.StringField.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.fields.simple.StringField", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "wtforms.fields.core._Widget"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["wtforms.form.BaseForm", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": ["wtforms.meta._SupportsGettextAndNgettext", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["wtforms.meta.DefaultMeta", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of StringField", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.simple.StringField.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.fields.simple.StringField", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.core._FormT", "id": -1, "name": "_FormT", "namespace": "wtforms.fields.simple.StringField.__init__", "upper_bound": "wtforms.form.BaseForm", "values": [], "variance": 0}]}}}, "data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "wtforms.fields.simple.StringField.data", "name": "data", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "default": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "wtforms.fields.simple.StringField.default", "name": "default", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.simple.StringField.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.fields.simple.StringField", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SubmitField": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["wtforms.fields.simple.BooleanField"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "wtforms.fields.simple.SubmitField", "name": "SubmitField", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "wtforms.fields.simple.SubmitField", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "wtforms.fields.simple", "mro": ["wtforms.fields.simple.SubmitField", "wtforms.fields.simple.BooleanField", "wtforms.fields.core.Field", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.simple.SubmitField.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.fields.simple.SubmitField", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TelField": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["wtforms.fields.simple.StringField"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "wtforms.fields.simple.TelField", "name": "TelField", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "wtforms.fields.simple.TelField", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "wtforms.fields.simple", "mro": ["wtforms.fields.simple.TelField", "wtforms.fields.simple.StringField", "wtforms.fields.core.Field", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.simple.TelField.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.fields.simple.TelField", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TextAreaField": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["wtforms.fields.simple.StringField"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "wtforms.fields.simple.TextAreaField", "name": "TextAreaField", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "wtforms.fields.simple.TextAreaField", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "wtforms.fields.simple", "mro": ["wtforms.fields.simple.TextAreaField", "wtforms.fields.simple.StringField", "wtforms.fields.core.Field", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.simple.TextAreaField.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.fields.simple.TextAreaField", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "URLField": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["wtforms.fields.simple.StringField"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "wtforms.fields.simple.URLField", "name": "URLField", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "wtforms.fields.simple.URLField", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "wtforms.fields.simple", "mro": ["wtforms.fields.simple.URLField", "wtforms.fields.simple.StringField", "wtforms.fields.core.Field", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.fields.simple.URLField.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.fields.simple.URLField", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_Filter": {".class": "SymbolTableNode", "cross_ref": "wtforms.fields.core._Filter", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_FormT": {".class": "SymbolTableNode", "cross_ref": "wtforms.fields.core._FormT", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_SupportsGettextAndNgettext": {".class": "SymbolTableNode", "cross_ref": "wtforms.meta._SupportsGettextAndNgettext", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_Validator": {".class": "SymbolTableNode", "cross_ref": "wtforms.fields.core._Validator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_Widget": {".class": "SymbolTableNode", "cross_ref": "wtforms.fields.core._Widget", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "wtforms.fields.simple.__all__", "name": "__all__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wtforms.fields.simple.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wtforms.fields.simple.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wtforms.fields.simple.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wtforms.fields.simple.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wtforms.fields.simple.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wtforms.fields.simple.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "C:\\Users\\<USER>\\Documents\\GitHub\\workshop\\.conda\\Lib\\site-packages\\wtforms-stubs\\fields\\simple.pyi"}