{"data_mtime": 1752486190, "dep_lines": [7, 6, 1, 3, 4, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 10, 5, 30, 30, 30], "dependencies": ["urllib3.util.util", "urllib3.exceptions", "__future__", "re", "typing", "builtins", "_frozen_importlib", "abc", "enum"], "hash": "51d396146d087d3f2d1ec80b00837381c16c6352", "id": "urllib3.util.url", "ignore_all": true, "interface_hash": "20ca701588e9518aafe01734accb336de587e4e9", "mtime": 1750271475, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Documents\\GitHub\\workshop\\.conda\\Lib\\site-packages\\urllib3\\util\\url.py", "plugin_data": null, "size": 15205, "suppressed": [], "version_id": "1.16.1"}