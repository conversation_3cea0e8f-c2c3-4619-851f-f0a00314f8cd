{"data_mtime": 1752486191, "dep_lines": [1, 6, 7, 2, 3, 4, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["collections.abc", "wtforms.fields", "wtforms.form", "decimal", "re", "typing", "builtins", "_frozen_importlib", "abc", "types", "wtforms.fields.core", "wtforms.fields.simple"], "hash": "e2e0b97d1033756869fab4ae65d838a700a456b7", "id": "wtforms.validators", "ignore_all": true, "interface_hash": "0a1b1ee73b41d1d69f1ee55f6546ca0baf732bc6", "mtime": 1752485783, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Documents\\GitHub\\workshop\\.conda\\Lib\\site-packages\\wtforms-stubs\\validators.pyi", "plugin_data": null, "size": 6758, "suppressed": [], "version_id": "1.16.1"}