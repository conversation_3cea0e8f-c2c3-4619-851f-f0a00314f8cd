{".class": "MypyFile", "_fullname": "app.models.user", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Base": {".class": "SymbolTableNode", "cross_ref": "app.core.database.Base", "kind": "Gdef"}, "Boolean": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.Boolean", "kind": "Gdef"}, "Column": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Column", "kind": "Gdef"}, "DateTime": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.DateTime", "kind": "Gdef"}, "ForeignKey": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.ForeignKey", "kind": "Gdef"}, "Integer": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.Integer", "kind": "Gdef"}, "String": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.String", "kind": "Gdef"}, "Text": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.Text", "kind": "Gdef"}, "UUID": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.UUID", "kind": "Gdef"}, "User": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.models.user.User", "name": "User", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "app.models.user.User", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app.models.user", "mro": ["app.models.user.User", "builtins.object"], "names": {".class": "SymbolTable", "__tablename__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "app.models.user.User.__tablename__", "name": "__tablename__", "setter_type": null, "type": "builtins.str"}}, "bio": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.models.user.User.bio", "name": "bio", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}}}, "branch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.models.user.User.branch", "name": "branch", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.relationships._RelationshipDeclared"}}}, "branch_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.models.user.User.branch_id", "name": "branch_id", "setter_type": null, "type": {".class": "Instance", "args": ["uuid.UUID"], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}}}, "created_at": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.models.user.User.created_at", "name": "created_at", "setter_type": null, "type": {".class": "Instance", "args": ["datetime.datetime"], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}}}, "department": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.models.user.User.department", "name": "department", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.relationships._RelationshipDeclared"}}}, "department_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.models.user.User.department_id", "name": "department_id", "setter_type": null, "type": {".class": "Instance", "args": ["uuid.UUID"], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}}}, "email": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.models.user.User.email", "name": "email", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}}}, "employee": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.models.user.User.employee", "name": "employee", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.relationships._RelationshipDeclared"}}}, "failed_login_attempts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.models.user.User.failed_login_attempts", "name": "failed_login_attempts", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}}}, "full_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.models.user.User.full_name", "name": "full_name", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}}}, "hashed_password": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.models.user.User.hashed_password", "name": "hashed_password", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}}}, "id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.models.user.User.id", "name": "id", "setter_type": null, "type": {".class": "Instance", "args": ["uuid.UUID"], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}}}, "increment_failed_attempts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.models.user.User.increment_failed_attempts", "name": "increment_failed_attempts", "type": null}}, "is_active": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.models.user.User.is_active", "name": "is_active", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}}}, "is_locked": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.models.user.User.is_locked", "name": "is_locked", "type": null}}, "is_superuser": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.models.user.User.is_superuser", "name": "is_superuser", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}}}, "is_verified": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.models.user.User.is_verified", "name": "is_verified", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}}}, "language": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.models.user.User.language", "name": "language", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}}}, "last_login": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.models.user.User.last_login", "name": "last_login", "setter_type": null, "type": {".class": "Instance", "args": ["datetime.datetime"], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}}}, "lockout_until": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.models.user.User.lockout_until", "name": "lockout_until", "setter_type": null, "type": {".class": "Instance", "args": ["datetime.datetime"], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}}}, "manager_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.models.user.User.manager_name", "name": "manager_name", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}}}, "oauth_applications": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.models.user.User.oauth_applications", "name": "oauth_applications", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.relationships._RelationshipDeclared"}}}, "position": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.models.user.User.position", "name": "position", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.relationships._RelationshipDeclared"}}}, "position_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.models.user.User.position_id", "name": "position_id", "setter_type": null, "type": {".class": "Instance", "args": ["uuid.UUID"], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}}}, "reset_failed_attempts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.models.user.User.reset_failed_attempts", "name": "reset_failed_attempts", "type": null}}, "roles": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.models.user.User.roles", "name": "roles", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.relationships._RelationshipDeclared"}}}, "timezone": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.models.user.User.timezone", "name": "timezone", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}}}, "to_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.models.user.User.to_dict", "name": "to_dict", "type": null}}, "updated_at": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.models.user.User.updated_at", "name": "updated_at", "setter_type": null, "type": {".class": "Instance", "args": ["datetime.datetime"], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}}}, "username": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.models.user.User.username", "name": "username", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.models.user.User.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.models.user.User", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.models.user.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.models.user.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.models.user.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.models.user.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.models.user.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.models.user.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "func": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.functions.func", "kind": "Gdef"}, "relationship": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._orm_constructors.relationship", "kind": "Gdef"}, "text": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.text", "kind": "Gdef"}, "timedelta": {".class": "SymbolTableNode", "cross_ref": "datetime.<PERSON><PERSON><PERSON>", "kind": "Gdef"}}, "path": ".\\app\\models\\user.py"}