{".class": "MypyFile", "_fullname": "wtforms.widgets.core", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CheckboxInput": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["wtforms.widgets.core.Input"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "wtforms.widgets.core.CheckboxInput", "name": "CheckboxInput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "wtforms.widgets.core.CheckboxInput", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "wtforms.widgets.core", "mro": ["wtforms.widgets.core.CheckboxInput", "wtforms.widgets.core.Input", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.widgets.core.CheckboxInput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.widgets.core.CheckboxInput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ColorInput": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["wtforms.widgets.core.Input"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "wtforms.widgets.core.ColorInput", "name": "ColorInput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "wtforms.widgets.core.ColorInput", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "wtforms.widgets.core", "mro": ["wtforms.widgets.core.ColorInput", "wtforms.widgets.core.Input", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.widgets.core.ColorInput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.widgets.core.ColorInput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DateInput": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["wtforms.widgets.core.Input"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "wtforms.widgets.core.DateInput", "name": "DateInput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "wtforms.widgets.core.DateInput", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "wtforms.widgets.core", "mro": ["wtforms.widgets.core.DateInput", "wtforms.widgets.core.Input", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.widgets.core.DateInput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.widgets.core.DateInput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DateTimeInput": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["wtforms.widgets.core.Input"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "wtforms.widgets.core.DateTimeInput", "name": "DateTimeInput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "wtforms.widgets.core.DateTimeInput", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "wtforms.widgets.core", "mro": ["wtforms.widgets.core.DateTimeInput", "wtforms.widgets.core.Input", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.widgets.core.DateTimeInput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.widgets.core.DateTimeInput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DateTimeLocalInput": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["wtforms.widgets.core.Input"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "wtforms.widgets.core.DateTimeLocalInput", "name": "DateTimeLocalInput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "wtforms.widgets.core.DateTimeLocalInput", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "wtforms.widgets.core", "mro": ["wtforms.widgets.core.DateTimeLocalInput", "wtforms.widgets.core.Input", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.widgets.core.DateTimeLocalInput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.widgets.core.DateTimeLocalInput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Decimal": {".class": "SymbolTableNode", "cross_ref": "decimal.Decimal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "EmailInput": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["wtforms.widgets.core.Input"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "wtforms.widgets.core.EmailInput", "name": "EmailInput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "wtforms.widgets.core.EmailInput", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "wtforms.widgets.core", "mro": ["wtforms.widgets.core.EmailInput", "wtforms.widgets.core.Input", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.widgets.core.EmailInput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.widgets.core.EmailInput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Field": {".class": "SymbolTableNode", "cross_ref": "wtforms.fields.core.Field", "kind": "Gdef", "module_hidden": true, "module_public": false}, "FileInput": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["wtforms.widgets.core.Input"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "wtforms.widgets.core.FileInput", "name": "FileInput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "wtforms.widgets.core.FileInput", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "wtforms.widgets.core", "mro": ["wtforms.widgets.core.FileInput", "wtforms.widgets.core.Input", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "multiple"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "wtforms.widgets.core.FileInput.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "multiple"], "arg_types": ["wtforms.widgets.core.FileInput", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of FileInput", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "multiple": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "wtforms.widgets.core.FileInput.multiple", "name": "multiple", "setter_type": null, "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.widgets.core.FileInput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.widgets.core.FileInput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FormField": {".class": "SymbolTableNode", "cross_ref": "wtforms.fields.form.FormField", "kind": "Gdef", "module_hidden": true, "module_public": false}, "HiddenInput": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["wtforms.widgets.core.Input"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "wtforms.widgets.core.HiddenInput", "name": "HiddenInput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "wtforms.widgets.core.HiddenInput", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "wtforms.widgets.core", "mro": ["wtforms.widgets.core.HiddenInput", "wtforms.widgets.core.Input", "builtins.object"], "names": {".class": "SymbolTable", "field_flags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "wtforms.widgets.core.HiddenInput.field_flags", "name": "field_flags", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.widgets.core.HiddenInput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.widgets.core.HiddenInput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Input": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "wtforms.widgets.core.Input", "name": "Input", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "wtforms.widgets.core.Input", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "wtforms.widgets.core", "mro": ["wtforms.widgets.core.Input", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "field", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "wtforms.widgets.core.Input.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "field", "kwargs"], "arg_types": ["wtforms.widgets.core.Input", "wtforms.fields.core.Field", "builtins.object"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of Input", "ret_type": "markupsafe.Markup", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "input_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "wtforms.widgets.core.Input.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "input_type"], "arg_types": ["wtforms.widgets.core.Input", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of Input", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "html_params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [4], "arg_names": ["kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "wtforms.widgets.core.Input.html_params", "name": "html_params", "type": {".class": "CallableType", "arg_kinds": [4], "arg_names": ["kwargs"], "arg_types": ["builtins.object"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "html_params of Input", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "wtforms.widgets.core.Input.html_params", "name": "html_params", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [4], "arg_names": ["kwargs"], "arg_types": ["builtins.object"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "html_params of Input", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "input_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "wtforms.widgets.core.Input.input_type", "name": "input_type", "setter_type": null, "type": "builtins.str"}}, "validation_attrs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "wtforms.widgets.core.Input.validation_attrs", "name": "validation_attrs", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.widgets.core.Input.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.widgets.core.Input", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ListWidget": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "wtforms.widgets.core.ListWidget", "name": "ListWidget", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "wtforms.widgets.core.ListWidget", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "wtforms.widgets.core", "mro": ["wtforms.widgets.core.ListWidget", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "field", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "wtforms.widgets.core.ListWidget.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "field", "kwargs"], "arg_types": ["wtforms.widgets.core.ListWidget", "wtforms.fields.core.Field", "builtins.object"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of ListWidget", "ret_type": "markupsafe.Markup", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "html_tag", "prefix_label"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "wtforms.widgets.core.ListWidget.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "html_tag", "prefix_label"], "arg_types": ["wtforms.widgets.core.ListWidget", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "ul"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ol"}], "uses_pep604_syntax": false}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ListWidget", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "html_tag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "wtforms.widgets.core.ListWidget.html_tag", "name": "html_tag", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "ul"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ol"}], "uses_pep604_syntax": false}}}, "prefix_label": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "wtforms.widgets.core.ListWidget.prefix_label", "name": "prefix_label", "setter_type": null, "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.widgets.core.ListWidget.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.widgets.core.ListWidget", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Markup": {".class": "SymbolTableNode", "cross_ref": "markupsafe.Markup", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MonthInput": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["wtforms.widgets.core.Input"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "wtforms.widgets.core.MonthInput", "name": "MonthInput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "wtforms.widgets.core.MonthInput", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "wtforms.widgets.core", "mro": ["wtforms.widgets.core.MonthInput", "wtforms.widgets.core.Input", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.widgets.core.MonthInput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.widgets.core.MonthInput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NumberInput": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["wtforms.widgets.core.Input"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "wtforms.widgets.core.NumberInput", "name": "NumberInput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "wtforms.widgets.core.NumberInput", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "wtforms.widgets.core", "mro": ["wtforms.widgets.core.NumberInput", "wtforms.widgets.core.Input", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "step", "min", "max"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "wtforms.widgets.core.NumberInput.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "step", "min", "max"], "arg_types": ["wtforms.widgets.core.NumberInput", {".class": "UnionType", "items": ["decimal.Decimal", "builtins.float", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["decimal.Decimal", "builtins.float", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["decimal.Decimal", "builtins.float", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of NumberInput", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "max": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "wtforms.widgets.core.NumberInput.max", "name": "max", "setter_type": null, "type": {".class": "UnionType", "items": ["decimal.Decimal", "builtins.float", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "min": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "wtforms.widgets.core.NumberInput.min", "name": "min", "setter_type": null, "type": {".class": "UnionType", "items": ["decimal.Decimal", "builtins.float", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "step": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "wtforms.widgets.core.NumberInput.step", "name": "step", "setter_type": null, "type": {".class": "UnionType", "items": ["decimal.Decimal", "builtins.float", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.widgets.core.NumberInput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.widgets.core.NumberInput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Option": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "wtforms.widgets.core.Option", "name": "Option", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "wtforms.widgets.core.Option", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "wtforms.widgets.core", "mro": ["wtforms.widgets.core.Option", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "field", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "wtforms.widgets.core.Option.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "field", "kwargs"], "arg_types": ["wtforms.widgets.core.Option", "wtforms.fields.choices.SelectFieldBase._Option", "builtins.object"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of Option", "ret_type": "markupsafe.Markup", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.widgets.core.Option.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.widgets.core.Option", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PasswordInput": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["wtforms.widgets.core.Input"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "wtforms.widgets.core.PasswordInput", "name": "PasswordInput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "wtforms.widgets.core.PasswordInput", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "wtforms.widgets.core", "mro": ["wtforms.widgets.core.PasswordInput", "wtforms.widgets.core.Input", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "hide_value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "wtforms.widgets.core.PasswordInput.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "hide_value"], "arg_types": ["wtforms.widgets.core.PasswordInput", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of PasswordInput", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "hide_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "wtforms.widgets.core.PasswordInput.hide_value", "name": "hide_value", "setter_type": null, "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.widgets.core.PasswordInput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.widgets.core.PasswordInput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RadioInput": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["wtforms.widgets.core.Input"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "wtforms.widgets.core.RadioInput", "name": "RadioInput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "wtforms.widgets.core.RadioInput", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "wtforms.widgets.core", "mro": ["wtforms.widgets.core.RadioInput", "wtforms.widgets.core.Input", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.widgets.core.RadioInput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.widgets.core.RadioInput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RangeInput": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["wtforms.widgets.core.Input"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "wtforms.widgets.core.RangeInput", "name": "RangeInput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "wtforms.widgets.core.RangeInput", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "wtforms.widgets.core", "mro": ["wtforms.widgets.core.RangeInput", "wtforms.widgets.core.Input", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "step"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "wtforms.widgets.core.RangeInput.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "step"], "arg_types": ["wtforms.widgets.core.RangeInput", {".class": "UnionType", "items": ["decimal.Decimal", "builtins.float", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of RangeInput", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "step": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "wtforms.widgets.core.RangeInput.step", "name": "step", "setter_type": null, "type": {".class": "UnionType", "items": ["decimal.Decimal", "builtins.float", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.widgets.core.RangeInput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.widgets.core.RangeInput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SearchInput": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["wtforms.widgets.core.Input"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "wtforms.widgets.core.SearchInput", "name": "SearchInput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "wtforms.widgets.core.SearchInput", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "wtforms.widgets.core", "mro": ["wtforms.widgets.core.SearchInput", "wtforms.widgets.core.Input", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.widgets.core.SearchInput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.widgets.core.SearchInput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Select": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "wtforms.widgets.core.Select", "name": "Select", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "wtforms.widgets.core.Select", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "wtforms.widgets.core", "mro": ["wtforms.widgets.core.Select", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "field", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "wtforms.widgets.core.Select.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "field", "kwargs"], "arg_types": ["wtforms.widgets.core.Select", "wtforms.fields.choices.SelectFieldBase", "builtins.object"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of Select", "ret_type": "markupsafe.Markup", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "multiple"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "wtforms.widgets.core.Select.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "multiple"], "arg_types": ["wtforms.widgets.core.Select", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of Select", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "multiple": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "wtforms.widgets.core.Select.multiple", "name": "multiple", "setter_type": null, "type": "builtins.bool"}}, "render_option": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["cls", "value", "label", "selected", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "wtforms.widgets.core.Select.render_option", "name": "render_option", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["cls", "value", "label", "selected", "kwargs"], "arg_types": [{".class": "TypeType", "item": "wtforms.widgets.core.Select"}, "builtins.object", "builtins.str", "builtins.bool", "builtins.object"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "render_option of Select", "ret_type": "markupsafe.Markup", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "wtforms.widgets.core.Select.render_option", "name": "render_option", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["cls", "value", "label", "selected", "kwargs"], "arg_types": [{".class": "TypeType", "item": "wtforms.widgets.core.Select"}, "builtins.object", "builtins.str", "builtins.bool", "builtins.object"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "render_option of Select", "ret_type": "markupsafe.Markup", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "validation_attrs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "wtforms.widgets.core.Select.validation_attrs", "name": "validation_attrs", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.widgets.core.Select.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.widgets.core.Select", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SelectFieldBase": {".class": "SymbolTableNode", "cross_ref": "wtforms.fields.choices.SelectFieldBase", "kind": "Gdef", "module_hidden": true, "module_public": false}, "StringField": {".class": "SymbolTableNode", "cross_ref": "wtforms.fields.simple.StringField", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SubmitInput": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["wtforms.widgets.core.Input"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "wtforms.widgets.core.SubmitInput", "name": "SubmitInput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "wtforms.widgets.core.SubmitInput", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "wtforms.widgets.core", "mro": ["wtforms.widgets.core.SubmitInput", "wtforms.widgets.core.Input", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.widgets.core.SubmitInput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.widgets.core.SubmitInput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TableWidget": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "wtforms.widgets.core.TableWidget", "name": "TableWidget", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "wtforms.widgets.core.TableWidget", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "wtforms.widgets.core", "mro": ["wtforms.widgets.core.TableWidget", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "field", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "wtforms.widgets.core.TableWidget.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "field", "kwargs"], "arg_types": ["wtforms.widgets.core.TableWidget", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "wtforms.fields.form.FormField"}, "builtins.object"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of TableWidget", "ret_type": "markupsafe.Markup", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "with_table_tag"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "wtforms.widgets.core.TableWidget.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "with_table_tag"], "arg_types": ["wtforms.widgets.core.TableWidget", "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of TableWidget", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "with_table_tag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "wtforms.widgets.core.TableWidget.with_table_tag", "name": "with_table_tag", "setter_type": null, "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.widgets.core.TableWidget.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.widgets.core.TableWidget", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TelInput": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["wtforms.widgets.core.Input"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "wtforms.widgets.core.TelInput", "name": "TelInput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "wtforms.widgets.core.TelInput", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "wtforms.widgets.core", "mro": ["wtforms.widgets.core.TelInput", "wtforms.widgets.core.Input", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.widgets.core.TelInput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.widgets.core.TelInput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TextArea": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "wtforms.widgets.core.TextArea", "name": "TextArea", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "wtforms.widgets.core.TextArea", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "wtforms.widgets.core", "mro": ["wtforms.widgets.core.TextArea", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "field", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "wtforms.widgets.core.TextArea.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "field", "kwargs"], "arg_types": ["wtforms.widgets.core.TextArea", "wtforms.fields.simple.StringField", "builtins.object"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of TextArea", "ret_type": "markupsafe.Markup", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "validation_attrs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "wtforms.widgets.core.TextArea.validation_attrs", "name": "validation_attrs", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.widgets.core.TextArea.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.widgets.core.TextArea", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TextInput": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["wtforms.widgets.core.Input"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "wtforms.widgets.core.TextInput", "name": "TextInput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "wtforms.widgets.core.TextInput", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "wtforms.widgets.core", "mro": ["wtforms.widgets.core.TextInput", "wtforms.widgets.core.Input", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.widgets.core.TextInput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.widgets.core.TextInput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TimeInput": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["wtforms.widgets.core.Input"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "wtforms.widgets.core.TimeInput", "name": "TimeInput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "wtforms.widgets.core.TimeInput", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "wtforms.widgets.core", "mro": ["wtforms.widgets.core.TimeInput", "wtforms.widgets.core.Input", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.widgets.core.TimeInput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.widgets.core.TimeInput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "URLInput": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["wtforms.widgets.core.Input"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "wtforms.widgets.core.URLInput", "name": "URLInput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "wtforms.widgets.core.URLInput", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "wtforms.widgets.core", "mro": ["wtforms.widgets.core.URLInput", "wtforms.widgets.core.Input", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.widgets.core.URLInput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.widgets.core.URLInput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "WeekInput": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["wtforms.widgets.core.Input"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "wtforms.widgets.core.WeekInput", "name": "WeekInput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "wtforms.widgets.core.WeekInput", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "wtforms.widgets.core", "mro": ["wtforms.widgets.core.WeekInput", "wtforms.widgets.core.Input", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "wtforms.widgets.core.WeekInput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "wtforms.widgets.core.WeekInput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_Option": {".class": "SymbolTableNode", "cross_ref": "wtforms.fields.choices._Option", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "wtforms.widgets.core.__all__", "name": "__all__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wtforms.widgets.core.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wtforms.widgets.core.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wtforms.widgets.core.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wtforms.widgets.core.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wtforms.widgets.core.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "wtforms.widgets.core.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "html_params": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [4], "arg_names": ["kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "wtforms.widgets.core.html_params", "name": "html_params", "type": {".class": "CallableType", "arg_kinds": [4], "arg_names": ["kwargs"], "arg_types": ["builtins.object"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "html_params", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "C:\\Users\\<USER>\\Documents\\GitHub\\workshop\\.conda\\Lib\\site-packages\\wtforms-stubs\\widgets\\core.pyi"}